"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Shield, Key, AlertTriangle } from "lucide-react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";


interface UserSecuritySettingsProps {
  userId: string;
  lastLoginAt?: string | null;
}

export default function UserSecuritySettings({ 
  userId, 
  lastLoginAt 
}: UserSecuritySettingsProps) {
  const router = useRouter();
  const [isEnabling2FA, setIsEnabling2FA] = useState(false);
  
  const handleEnable2FA = async () => {
    setIsEnabling2FA(true);
    try {
      // In a real implementation, this would redirect to Clerk's 2FA setup
      // For now, we'll just show a toast
      toast.info("Redirecting to two-factor authentication setup...");
      
      // Redirect to Clerk's 2FA setup page
      window.location.href = "https://accounts.clerk.dev/user/settings/security";
    } catch (error) {
      console.error("Error enabling 2FA:", error);
      toast.error("Failed to enable two-factor authentication");
    } finally {
      setIsEnabling2FA(false);
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Security Settings
        </CardTitle>
        <CardDescription>
          Manage your account security preferences
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <h3 className="text-sm font-medium mb-2">Two-Factor Authentication</h3>
          <p className="text-sm text-gray-500 mb-4">
            Add an extra layer of security to your account by requiring a verification code in addition to your password.
          </p>
          <Button 
            variant="outline" 
            onClick={handleEnable2FA}
            disabled={isEnabling2FA}
          >
            <Key className="mr-2 h-4 w-4" />
            {isEnabling2FA ? "Setting up..." : "Set up two-factor authentication"}
          </Button>
        </div>
        
    
        <div>
          <h3 className="text-sm font-medium mb-2">Recent Activity</h3>
          <p className="text-sm text-gray-500">
            Last login: {lastLoginAt ? lastLoginAt : "Never"}
          </p>
          <Button 
            variant="link" 
            className="p-0 h-auto text-sm"
            onClick={() => router.push("/settings/activity")}
          >
            View all activity
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

