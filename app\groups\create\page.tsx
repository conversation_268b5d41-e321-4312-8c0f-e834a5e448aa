"use client";


import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import GroupForm from "@/app/components/groups/GroupForm";
import { createGroup } from "@/app/actions/groupActions";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

export default function CreateGroupPage() {
  const router = useRouter();

  const handleSubmit = async (data: { name: string; permissions: string[]; description?: string }) => {
    try {
      const result = await createGroup(data);
      if (result.status === "SUCCESS") {
        toast.success("Group created successfully");
        router.push("/groups");
      } else {
        toast.error(result.message || "Failed to create group");
      }
    } catch (error) {
      console.error("Error creating group:", error);
      toast.error("Failed to create group");
    }
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center gap-4 mb-6">
        <Link href="/groups">
          <Button variant="outline" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-3xl font-bold">Create Access Group</h1>
      </div>

      <div className="max-w-2xl">
        <GroupForm onSubmit={handleSubmit} />
      </div>
    </div>
  );
}