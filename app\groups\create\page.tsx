import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import GroupForm from "@/app/components/groups/GroupForm";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { createGroup } from "@/app/actions/groupActions";

export const metadata: Metadata = {
  title: "Create Access Group",
  description: "Create a new access group",
};

export default async function CreateGroupPage() {
  await requireAdminOrModerator();

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center gap-4 mb-6">
        <Link href="/groups">
          <Button variant="outline" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-3xl font-bold">Create Access Group</h1>
      </div>
      
      <div className="max-w-2xl">
        <GroupForm onSubmit={createGroup} />
      </div>
    </div>
  );
}