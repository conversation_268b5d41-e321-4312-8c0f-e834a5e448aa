import { ProductDetailsInterface } from "@/app/types/Types";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import Link from "next/link";

interface ProductDetailsProps {
  foundProductDetails: ProductDetailsInterface;
}

export default function ProductDetails({ foundProductDetails  }: ProductDetailsProps) {
    return (
        <Card className="mb-4">
          <CardHeader>
            <CardTitle>Details</CardTitle> {/* Use CardTitle assuming it exists */}
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Material Number</TableHead>
                  <TableHead>New_Material</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Cross_Plant</TableHead>
                  <TableHead>Base_Unit_Of_Measur</TableHead>
                  <TableHead>Material Group</TableHead>
                  <TableHead>Net Weight</TableHead>
                  <TableHead>Pret AM</TableHead>
                  <TableHead>Pret Final</TableHead>
                  <TableHead>Has Discount</TableHead>
                  <TableHead>Active Discount Type</TableHead>
                  <TableHead>Active Discount Value</TableHead>
                  <TableHead>Discount Percentage</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell>{foundProductDetails.Material_Number}</TableCell>
                  <TableCell>{foundProductDetails.New_Material}</TableCell>
                  <TableCell>{foundProductDetails.Description_Local}</TableCell>
                  <TableCell>{foundProductDetails.Cross_Plant}</TableCell>
                  <TableCell>{foundProductDetails.Base_Unit_Of_Measur}</TableCell>
                  <TableCell>{foundProductDetails.Material_Group}</TableCell>
                  <TableCell>{foundProductDetails.Net_Weight}</TableCell>
                  <TableCell>{foundProductDetails.PretAM === null ? "NULL" : foundProductDetails.PretAM}</TableCell>
                  <TableCell>{foundProductDetails.FinalPrice === null ? "NULL" : foundProductDetails.FinalPrice}</TableCell>
                  <TableCell>{foundProductDetails.HasDiscount ? "Yes" : "No"}</TableCell>
                  <TableCell>{foundProductDetails.activeDiscountType ? foundProductDetails.activeDiscountType : "NULL"}</TableCell>
                  <TableCell>{foundProductDetails.activeDiscountValue === null ? "NULL" : foundProductDetails.activeDiscountValue}</TableCell>
                  <TableCell>{foundProductDetails.discountPercentage === null ? "NULL" : foundProductDetails.discountPercentage}</TableCell>
                </TableRow>
              </TableBody>
            </Table>

            <Link className="text-blue-500 underline ml-2" href={`/product/attributes/${foundProductDetails.Material_Number}`}>Go to Attributes</Link> <br></br>
            <Link className="text-blue-500 underline ml-2" href={`/product/history/${foundProductDetails.Material_Number}`}>Go to History</Link> <br></br>
            <Link className="text-blue-500 underline ml-2" href={`/product/priceHistory/${foundProductDetails.Material_Number}`}>Go to Price History</Link>
          </CardContent>
        </Card>
    )
}
