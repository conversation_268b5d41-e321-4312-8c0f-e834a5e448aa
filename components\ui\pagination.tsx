"use client"

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  siblingsCount?: number;
}

export function Pagination({
  currentPage,
  totalPages,
  onPageChange,
  siblingsCount = 1,
}: PaginationProps) {
  // Generate page numbers to display
  const generatePages = () => {
    // Always show first page, last page, current page, and siblings
    const pages = new Set<number>();
    
    // Add current page and siblings
    for (let i = Math.max(1, currentPage - siblingsCount); i <= Math.min(totalPages, currentPage + siblingsCount); i++) {
      pages.add(i);
    }
    
    // Add first and last pages
    pages.add(1);
    pages.add(totalPages);
    
    // Convert to array and sort
    return Array.from(pages).sort((a, b) => a - b);
  };
  
  const pages = generatePages();
  
  // Create the final array with ellipses
  const paginationItems = pages.reduce<(number | string)[]>((items, page, index) => {
    // Add the page
    items.push(page);
    
    // Add ellipsis if there's a gap
    if (index < pages.length - 1 && pages[index + 1] - page > 1) {
      items.push("ellipsis");
    }
    
    return items;
  }, []);
  
  return (
    <div className="flex items-center gap-1">
      <Button
        variant="outline"
        size="icon"
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage <= 1}
      >
        <ChevronLeft className="h-4 w-4" />
        <span className="sr-only">Previous page</span>
      </Button>
      
      {paginationItems.map((item, index) => {
        if (item === "ellipsis") {
          return (
            <Button
              key={`ellipsis-${index}`}
              variant="ghost"
              size="icon"
              disabled
            >
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">More pages</span>
            </Button>
          );
        }
        
        const page = item as number;
        return (
          <Button
            key={page}
            variant={currentPage === page ? "default" : "outline"}
            size="icon"
            onClick={() => onPageChange(page)}
          >
            {page}
            <span className="sr-only">Page {page}</span>
          </Button>
        );
      })}
      
      <Button
        variant="outline"
        size="icon"
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage >= totalPages}
      >
        <ChevronRight className="h-4 w-4" />
        <span className="sr-only">Next page</span>
      </Button>
    </div>
  );
}