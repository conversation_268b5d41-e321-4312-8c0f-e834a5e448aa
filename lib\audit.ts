
import prisma from "@/app/utils/db";

interface UserAuditLogData {
  userId: string;
  action: string;
  entityType: string;
  entityId: string;
  details?: string;
  ipAddress?: string;
  userAgent?: string;
  performedBy?: string;
}

export async function createUserAuditLog(data: UserAuditLogData) {
  try {
    return await prisma.userAuditLog.create({
      data: {
        userId: data.userId,
        action: data.action,
        entityType: data.entityType,
        entityId: data.entityId,
        details: data.details || null,
        ipAddress: data.ipAddress || "server-side",
        userAgent: data.userAgent || "server-side",
        performedBy: data.performedBy || "system"
      }
    });
  } catch (error) {
    console.error("Error creating audit log:", error);
    // Don't throw - audit logs should not break functionality
    return null;
  }
}

export async function getUserAuditLogs({
  userId,
  action,
  entityType,
  entityId,
  limit = 50,
  offset = 0
}: {
  userId?: string;
  action?: string;
  entityType?: string;
  entityId?: string;
  limit?: number;
  offset?: number;
}) {
  try {
    const where: any = {};
    
    if (userId) where.userId = userId;
    if (action) where.action = action;
    if (entityType) where.entityType = entityType;
    if (entityId) where.entityId = entityId;
    
    const [logs, total] = await Promise.all([
      prisma.userAuditLog.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      }),
      prisma.userAuditLog.count({ where })
    ]);
    
    return { logs, total };
  } catch (error) {
    console.error("Error fetching audit logs:", error);
    return { logs: [], total: 0 };
  }
}
