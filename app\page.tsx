import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { SignedIn, SignedOut } from "@clerk/nextjs";
import { currentUser } from "@clerk/nextjs/server";
import { Package, ShoppingCart, Tag, Users } from "lucide-react";
import prisma from "./utils/db";
          import ReturnsDashboard from "@/app/components/dashboard/ReturnsDashboard";
import { requireAdminOrModerator } from "@/lib/auth-utils";

//function that will return the count of products
const getProductCount = async () => {
  const count = await prisma.product.count();
  return count;
};

//function that will return the count of orders
const getOrderCount = async () => {
  const count = await prisma.order.count();
  return count;
};

export default async function Home() {
  const user =await requireAdminOrModerator();
  
  return (
    <div className="container mx-auto py-8">
      <SignedOut>
        <div className="flex flex-col items-center justify-center py-12">
          <h1 className="text-4xl font-bold text-center mb-4">Welcome to Parts Database</h1>
          <p className="text-xl text-center text-gray-600 mb-8 max-w-2xl">
            Manage your automotive parts inventory, track orders, and more with our comprehensive database system.
          </p>
          <div className="flex gap-4">
            <Button asChild size="lg">
              <Link href="/sign-up">Get Started</Link>
            </Button>
            <Button variant="outline" asChild size="lg">
              <Link href="/sign-in">Sign In</Link>
            </Button>
          </div>
        </div>
      </SignedOut>
      
      <SignedIn>
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <p className="text-gray-500 mt-1">Welcome back, {user?.firstName}!</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Products</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">{await getProductCount()}</div>
                <Package className="h-5 w-5 text-gray-400" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Active Orders</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">56</div>
                <ShoppingCart className="h-5 w-5 text-gray-400" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Active Discounts</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">12</div>
                <Tag className="h-5 w-5 text-gray-400" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">789</div>
                <Users className="h-5 w-5 text-gray-400" />
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Orders</CardTitle>
              <CardDescription>
                Latest orders from your customers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-500">No recent orders found.</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Popular Products</CardTitle>
              <CardDescription>
                Most viewed products this month
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-500">No popular products found.</p>
            </CardContent>
          </Card>

          <div className="col-span-1">
            <ReturnsDashboard />
          </div>
          
        </div>
      </SignedIn>
    </div>
  );
}

