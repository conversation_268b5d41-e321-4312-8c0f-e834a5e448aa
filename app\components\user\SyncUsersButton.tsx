"use client";

import { useState, useTransition } from "react";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { syncUsers } from "@/app/actions/userActions";

export default function SyncUsersButton() {
  const [isPending, startTransition] = useTransition();
  const router = useRouter();

  const handleSyncUsers = () => {
    startTransition(async () => {
      try {
        const result = await syncUsers();
        
        if (result.status === "SUCCESS") {
          toast.success(result.message);
          router.refresh();
        } else {
          toast.error(result.message);
        }
      } catch (error) {
        console.error("Error syncing users:", error);
        toast.error("An unexpected error occurred while syncing users");
      }
    });
  };

  return (
    <Button 
      variant="outline" 
      onClick={handleSyncUsers}
      disabled={isPending}
    >
      <RefreshCw className={`mr-2 h-4 w-4 ${isPending ? 'animate-spin' : ''}`} />
      {isPending ? "Syncing..." : "Sync Users"}
    </Button>
  );
}