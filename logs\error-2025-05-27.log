{"level":"error","message":"DiscountAddProductAction -> Database error: PrismaClientValidationError: \nInvalid `prisma.productHistory.create()` invocation:\n\n{\n  data: {\n    change_type: \"DISCOUNT\",\n                 ~~~~~~~~~~\n    changed_by: \"cosmin.oprea\",\n    changes: {\n      discountName: \"disc1\",\n      discountValue: new Prisma.Decimal(\"44\")\n    },\n    snapshot: {\n      discountId: \"cmb638k40000lhxis0uavqec4\",\n      productId: \"cmb0j303100rnrshx5i8h5leh\",\n      materialNumber: \"11127502650\"\n    },\n    version: 3,\n    Material_Number: \"11127502650\"\n  }\n}\n\nInvalid value for argument `change_type`. Expected ChangeType.  by cosmin.oprea","timestamp":"2025-05-27 08:44:54"}
{"level":"error","message":"DiscountAddProductAction -> Database error: PrismaClientValidationError: \nInvalid `prisma.productHistory.create()` invocation:\n\n{\n  data: {\n    change_type: \"DISCOUNT\",\n                 ~~~~~~~~~~\n    changed_by: \"cosmin.oprea\",\n    changes: {\n      discountName: \"disc1\",\n      discountValue: new Prisma.Decimal(\"44\")\n    },\n    snapshot: {\n      discountId: \"cmb638k40000lhxis0uavqec4\",\n      productId: \"cmb0j02lb0000rshxfwlf2ug9\",\n      materialNumber: \"01090398983\"\n    },\n    version: 4,\n    Material_Number: \"01090398983\"\n  }\n}\n\nInvalid value for argument `change_type`. Expected ChangeType.  by cosmin.oprea","timestamp":"2025-05-27 08:46:07"}
{"level":"error","message":"DiscountAddProductAction -> Product 01090398983 already has an active discount by cosmin.oprea","timestamp":"2025-05-27 12:10:41"}
{"level":"error","message":"DiscountAddProductAction -> Product 01090398983 already has an active discount by cosmin.oprea","timestamp":"2025-05-27 12:17:43"}
{"level":"error","message":"DiscountAddProductAction -> Product 01090398983 already has an active discount by cosmin.oprea","timestamp":"2025-05-27 12:23:41"}
{"level":"error","message":"DiscountAddProductAction -> Database error: PrismaClientKnownRequestError: \nInvalid `prisma.productDiscount.create()` invocation:\n\n\nUnique constraint failed on the fields: (`productId`,`discountId`)  by cosmin.oprea","timestamp":"2025-05-27 12:31:10"}
{"level":"error","message":"DiscountProcessCSVAction -> No material numbers provided  by cosmin.oprea","timestamp":"2025-05-27 12:35:51"}
{"level":"error","message":"DiscountProcessCSVAction -> No material numbers provided  by cosmin.oprea","timestamp":"2025-05-27 12:35:58"}
{"level":"error","message":"DiscountProcessCSVAction -> Database error: PrismaClientValidationError: \nInvalid `prisma.discountHistory.create()` invocation:\n\n{\n  data: {\n    change_type: \"PRODUCT_ADD_CSV\",\n                 ~~~~~~~~~~~~~~~~~\n    changes: {\n      addedProductsCount: 3,\n      addedMaterialNumbers: [\n        \"01090398983\",\n        \"01200411868\",\n        \"01200426532\"\n      ]\n    },\n    changed_by: \"cosmin.oprea\",\n    snapshot: {\n      description: \"let`s go\",\n      type: \"FIXED_AMOUNT\",\n      value: new Prisma.Decimal(\"233\"),\n      startDate: new Date(\"2025-05-30T09:10:00.000Z\"),\n      endDate: new Date(\"2025-05-31T09:10:00.000Z\"),\n      active: false,\n      createdBy: \"cosmin.oprea\"\n    },\n    version: 3,\n    discountId: \"cmb6apw51001ihxzk0dfmvjxp\"\n  }\n}\n\nInvalid value for argument `change_type`. Expected ChangeType.  by cosmin.oprea","timestamp":"2025-05-27 13:31:23"}
{"level":"error","message":"DiscountProcessCSVAction -> Database error: PrismaClientValidationError: \nInvalid `prisma.discountHistory.create()` invocation:\n\n{\n  data: {\n    change_type: \"PRODUCT_ADD_CSV\",\n                 ~~~~~~~~~~~~~~~~~\n    changes: {\n      addedProductsCount: 3,\n      addedMaterialNumbers: [\n        \"01090398983\",\n        \"01200411868\",\n        \"01200426532\"\n      ]\n    },\n    changed_by: \"cosmin.oprea\",\n    snapshot: {\n      description: \"let`s go\",\n      type: \"FIXED_AMOUNT\",\n      value: new Prisma.Decimal(\"233\"),\n      startDate: new Date(\"2025-05-30T09:10:00.000Z\"),\n      endDate: new Date(\"2025-05-31T09:10:00.000Z\"),\n      active: false,\n      createdBy: \"cosmin.oprea\"\n    },\n    version: 3,\n    discountId: \"cmb6apw51001ihxzk0dfmvjxp\"\n  }\n}\n\nInvalid value for argument `change_type`. Expected ChangeType.  by cosmin.oprea","timestamp":"2025-05-27 13:31:32"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Attribute with key \"key2\" already exists for this product by cosmin.oprea","timestamp":"2025-05-27 14:08:45"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Attribute with key \"key1\" already exists for this product by cosmin.oprea","timestamp":"2025-05-27 14:09:04"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Attribute with key \"key3\" already exists for this product by cosmin.oprea","timestamp":"2025-05-27 14:09:19"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Attribute with key \"key1\" already exists for this product by cosmin.oprea","timestamp":"2025-05-27 14:09:34"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Attribute with key \"key2\" already exists for this product by cosmin.oprea","timestamp":"2025-05-27 14:10:11"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Processing stopped: Found 4 invalid items for 11127502650,01090398983,01410012466,01200411868. No attributes were added. for 11127502650,01090398983,01410012466,01200411868 by cosmin.oprea","timestamp":"2025-05-27 15:20:45"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Processing stopped: Found 1 invalid items for 01200411868. No attributes were added. for 01200411868 by cosmin.oprea","timestamp":"2025-05-27 15:21:47"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Processing stopped: Found 1 invalid items for 01200411868. No attributes were added. for 01200411868 by cosmin.oprea","timestamp":"2025-05-27 15:23:54"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Processing stopped: Found 0 invalid items for . No attributes were added. for  by cosmin.oprea","timestamp":"2025-05-27 15:24:17"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Processing stopped: Found 0 invalid items for . No attributes were added. for  by cosmin.oprea","timestamp":"2025-05-27 15:24:30"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Processing stopped: Found 0 invalid items for . No attributes were added. for  by cosmin.oprea","timestamp":"2025-05-27 15:24:38"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Processing stopped: Found 0 invalid items for . No attributes were added. for  by cosmin.oprea","timestamp":"2025-05-27 15:25:10"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Processing stopped: Found 0 invalid items for . No attributes were added. for  by cosmin.oprea","timestamp":"2025-05-27 15:25:28"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Processing stopped: Found 0 invalid items for . No attributes were added. for  by cosmin.oprea","timestamp":"2025-05-27 15:25:41"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Processing stopped: Found 4 invalid items for 11127502650,01090398983,01410012466,01200411868. No attributes were added. for 11127502650,01090398983,01410012466,01200411868 by cosmin.oprea","timestamp":"2025-05-27 15:26:36"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Processing stopped: Found 4 invalid items for 11127502650,01090398983,01410012466,01200411868. No attributes were added. for 11127502650,01090398983,01410012466,01200411868 by cosmin.oprea","timestamp":"2025-05-27 15:26:48"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Processing stopped: Found 4 invalid items for 11127502650,01090398983,01410012466,01200411868. No attributes were added. for 11127502650,01090398983,01410012466,01200411868 by cosmin.oprea","timestamp":"2025-05-27 15:28:10"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Processing stopped: Found 0 invalid items for . No attributes were added. for  by cosmin.oprea","timestamp":"2025-05-27 15:32:36"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Processing stopped: Found 2 invalid items for 01090398983,01099760404. No attributes were added. for 01090398983,01099760404 by cosmin.oprea","timestamp":"2025-05-27 15:35:53"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Processing stopped: Found 2 invalid items for 01090398983,01099760404. No attributes were added. for 01090398983,01099760404 by cosmin.oprea","timestamp":"2025-05-27 15:37:16"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Processing stopped: Found 2 invalid items for 01090398983,01099760404. No attributes were added. for 01090398983,01099760404 by cosmin.oprea","timestamp":"2025-05-27 15:39:48"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Processing stopped: Found 2 invalid items for 01090398983,01099760404. No attributes were added. for 01090398983,01099760404 by cosmin.oprea","timestamp":"2025-05-27 15:46:01"}
{"level":"error","message":"DiscountProcessCSVAction -> No material numbers provided  by cosmin.oprea","timestamp":"2025-05-27 16:19:23"}
{"level":"error","message":"DiscountProcessCSVAction -> Missing products: 1099760404;, 1200396439;, 1200411868;, 1200416326;, 1200426528;  by cosmin.oprea","timestamp":"2025-05-27 16:20:16"}
{"level":"error","message":"DiscountAddProductAction -> Product 01090398983 already has an active discount by cosmin.oprea","timestamp":"2025-05-27 16:48:53"}
{"level":"error","message":"DiscountAddProductAction -> Product \"00000000000\" not found by cosmin.oprea","timestamp":"2025-05-27 16:52:10"}
