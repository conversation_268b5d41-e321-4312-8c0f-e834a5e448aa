import { notFound } from "next/navigation";
import Link from "next/link";
import { getUserById } from "@/app/getData/user/data";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Edit, Shield, UserCog, Clock } from "lucide-react";
import { formatDistanceToNow, format } from "date-fns";
import UserActivityLog from "@/app/components/user/UserActivityLog";
import UserSecuritySettings from "@/app/components/user/UserSecuritySettings";
import UserAccessGroups from "@/app/components/user/UserAccessGroups";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { getAvailableGroupsForUser } from "@/app/getData/groups/data";

interface UserPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function UserPage({ params }: UserPageProps) {
  const currentUser = await requireAdminOrModerator();
  const paramsObject = await params;
  const idAsString = paramsObject.id;    //All params are strings and need to be parsed
  const user = await getUserById(idAsString);

  if (!user) {
    notFound();
  }
  
  // Function to get user status badge
  const getUserStatusBadge = () => {
    if (user.deletedAt) {
      return <Badge variant="destructive">Deleted</Badge>;
    }
    if (user.isSuspended) {
      return <Badge variant="secondary">Suspended</Badge>;
    }
    if (!user.isActive) {
      return <Badge variant="outline">Inactive</Badge>;
    }
    return <Badge variant="default">Active</Badge>;
  };
  
  // Function to get user role badge
  const getUserRoleBadge = () => {
    switch (user.role) {
      case "administAB":
        return <Badge variant="default">Administrator</Badge>;
      case "moderatorAB":
        return <Badge variant="outline">Moderator</Badge>;
      case "angajatAB":
        return <Badge variant="secondary">Employee</Badge>;
      case "fourLvlAdminAB":
        return <Badge variant="default">L4 Admin</Badge>;
      case "fourLvlInregistratAB":
        return <Badge variant="outline">L4 User</Badge>;
      default:
        return <Badge variant="outline">User</Badge>;
    }
  };

  const availableGroups = await getAvailableGroupsForUser(user.id);

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center gap-2 mb-6">
        <Link href="/users">
          <Button variant="outline" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-3xl font-bold">User Profile</h1>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div className="flex flex-col items-center">
                  <Avatar className="h-24 w-24 mb-4">
                    <AvatarImage src={user.profileImage} alt={`${user.firstName} ${user.lastName}`} />
                    <AvatarFallback className="text-2xl">
                      {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <CardTitle className="text-xl text-center">{user.firstName} {user.lastName}</CardTitle>
                  <CardDescription className="text-center">{user.email}</CardDescription>
                </div>
                <Link href={`/users/${user.id}/edit`}>
                  <Button variant="ghost" size="icon">
                    <Edit className="h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-center gap-2 mt-2">
                  {getUserRoleBadge()}
                  {getUserStatusBadge()}
                </div>
                
                <div className="pt-4 border-t">
                  <h3 className="font-medium mb-2">Contact Information</h3>
                  {user.phoneNumber && (
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-500">Phone:</span>
                      <span>{user.phoneNumber}</span>
                    </div>
                  )}
                  {user.department && (
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-500">Department:</span>
                      <span>{user.department}</span>
                    </div>
                  )}
                  {user.jobTitle && (
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-500">Job Title:</span>
                      <span>{user.jobTitle}</span>
                    </div>
                  )}
                </div>

                <div className="pt-4 border-t">
                  <h3 className="font-medium mb-2">Additional Information</h3>
                    {user.salutation && (
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-500">Salutation:</span>
                        <span>{user.salutation}</span>
                      </div>
                    )}
                    {user.userAM && (
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-500">User AM:</span>
                        <span>{user.userAM}</span>
                      </div>
                    )}
                    {user.bio && (
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-500">Bio:</span>
                        <span>{user.bio}</span>
                      </div>
                    )}
                    {user.preferredLanguage && (
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-500">Language:</span>
                        <span>{user.preferredLanguage}</span>
                      </div>
                    )}
                    {user.timezone && (
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-500">Timezone:</span>
                        <span>{user.timezone}</span>
                      </div>
                    )}
                    {user.newsletterOptIn && (
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-500">Newsletter:</span>
                        <span>{user.newsletterOptIn ? "Yes" : "No"}</span>
                      </div>
                    )}
                </div>
                
                <div className="pt-4 border-t">
                  <h3 className="font-medium mb-2">Account Information</h3>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-500">User ID:</span>
                    <span className="font-mono text-xs">{user.id.substring(0, 8)}...</span>
                  </div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-500">Created:</span>
                    <span title={format(new Date(user.createdAt), 'PPpp')}>
                      {formatDistanceToNow(new Date(user.createdAt), { addSuffix: true })}
                    </span>
                  </div>
                  {user.lastLoginAt && (
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-500">Last Login:</span>
                      <span title={format(new Date(user.lastLoginAt), 'PPpp')}>
                        {formatDistanceToNow(new Date(user.lastLoginAt), { addSuffix: true })}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
          
          {user.accessGroups.length > 0 && (
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="text-lg">Access Groups</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {user.accessGroups.map((group) => (
                    <Badge key={group.id} variant="outline">
                      {group.name}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
        
        <div className="md:col-span-2">
          <Tabs defaultValue="activity">
            <TabsList className="grid grid-cols-3 mb-6">
              <TabsTrigger value="activity">
                <Clock className="mr-2 h-4 w-4" />
                Activity
              </TabsTrigger>
              <TabsTrigger value="security">
                <Shield className="mr-2 h-4 w-4" />
                Security
              </TabsTrigger>
              {/* this tab will be available only for admins */}
              {currentUser.role === "administAB" && (
                <TabsTrigger value="access">
                  <UserCog className="mr-2 h-4 w-4" />
                  Access Control
                </TabsTrigger>
              )}
            </TabsList>
            
            <TabsContent value="activity" className="mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>Activity Log</CardTitle>
                  <CardDescription>
                    Recent user activity and system events
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <UserActivityLog
                    userId={user.id}
                    auditLogs={user.auditLogs.filter(log => log.userId !== null).map(log => ({
                      ...log,
                      userId: log.userId!
                    }))}
                  />
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="security" className="mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>Security Settings</CardTitle>
                  <CardDescription>
                    Manage user security settings and authentication options
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <UserSecuritySettings
                    userId={user.id} 
                    lastLoginAt={user.lastLoginAt?.toString()}
                  />
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* this tab will be available only for admins */}
            {currentUser.role === "administAB" && (
              <TabsContent value="access" className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle>Access Control</CardTitle>
                    <CardDescription>
                      Manage user permissions and access groups
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <UserAccessGroups
                      userId={user.id} 
                      userRole={user.role} 
                      accessGroups={user.accessGroups}
                      availableGroups={availableGroups}
                    />
                  </CardContent>
                </Card>
              </TabsContent>
            )}
          </Tabs>
        </div>
      </div>
    </div>
  );
}



