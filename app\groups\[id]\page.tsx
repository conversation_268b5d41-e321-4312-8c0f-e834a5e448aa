import { <PERSON>ada<PERSON> } from "next";
import { notFound } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ArrowLeft, Edit, Users, Shield, UserPlus } from "lucide-react";
import { format, formatDistanceToNow } from "date-fns";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { getGroupById } from "@/app/getData/groups/data";

interface GroupPageProps {
  params: Promise<{
    id: string;
  }>;
}

export async function generateMetadata({ params }: GroupPageProps): Promise<Metadata> {
  const paramsObject = await params;
  const group = await getGroupById(paramsObject.id);
  
  return {
    title: group ? `${group.name} - Access Group` : "Group Not Found",
    description: group?.description || "Access group details",
  };
}

export default async function GroupPage({ params }: GroupPageProps) {
  await requireAdminOrModerator();
  const paramsObject = await params;
  const group = await getGroupById(paramsObject.id);

  if (!group) {
    notFound();
  }

  const handleAddMember = () => {
    // Implement logic to add a member to the group
  };


  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center gap-4 mb-6">
        <Link href="/groups">
          <Button variant="outline" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div className="flex-1">
          <h1 className="text-3xl font-bold">{group.name}</h1>
          {group.description && (
            <p className="text-gray-500 mt-1">{group.description}</p>
          )}
        </div>
        <Link href={`/groups/${group.id}/edit`}>
          <Button>
            <Edit className="mr-2 h-4 w-4" />
            Edit Group
          </Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Group Information */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Group Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium text-gray-500">Group Name</p>
                <p className="text-lg font-semibold">{group.name}</p>
              </div>
              
              {group.description && (
                <div>
                  <p className="text-sm font-medium text-gray-500">Description</p>
                  <p className="text-sm">{group.description}</p>
                </div>
              )}
              
              <div>
                <p className="text-sm font-medium text-gray-500">Total Members</p>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  <span className="text-lg font-semibold">{group._count?.users || 0}</span>
                </div>
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-500">Created</p>
                <p className="text-sm" title={format(new Date(group.createdAt), 'PPpp')}>
                  {formatDistanceToNow(new Date(group.createdAt), { addSuffix: true })}
                </p>
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-500">Last Updated</p>
                <p className="text-sm" title={format(new Date(group.updatedAt), 'PPpp')}>
                  {formatDistanceToNow(new Date(group.updatedAt), { addSuffix: true })}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Permissions */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Permissions</CardTitle>
              <CardDescription>
                Permissions granted to members of this group
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {group.permissions.map((permission) => (
                  <Badge key={permission} variant="outline">
                    {permission}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Group Members */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Group Members ({group._count?.users || 0})
                  </CardTitle>
                  <CardDescription>
                    Users who are members of this access group
                  </CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  <UserPlus className="mr-2 h-4 w-4" />
                  Add Member
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {group.users.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Users className="mx-auto h-10 w-10 text-muted-foreground/60" />
                  <h3 className="mt-4 text-lg font-semibold">No Members</h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    This group doesn't have any members yet.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {group.users.map((user) => (
                    <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={user.profileImage} alt={`${user.firstName} ${user.lastName}`} />
                          <AvatarFallback>
                            {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{user.firstName} {user.lastName}</p>
                          <p className="text-sm text-gray-500">{user.email}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">
                          {user.role === "administAB" ? "Administrator" :
                           user.role === "moderatorAB" ? "Moderator" :
                           user.role === "angajatAB" ? "Employee" :
                           user.role === "fourLvlAdminAB" ? "L4 Admin" :
                           user.role === "fourLvlInregistratAB" ? "L4 User" : "User"}
                        </Badge>
                        <Link href={`/users/${user.id}`}>
                          <Button variant="ghost" size="sm">
                            View
                          </Button>
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
