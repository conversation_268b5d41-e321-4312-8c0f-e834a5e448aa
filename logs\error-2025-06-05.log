{"level":"error","message":"DiscountAddProductAction -> Product 11422247017 already has an active discount by cosmin.oprea","timestamp":"2025-06-05 08:13:01"}
{"level":"error","message":"DiscountAddProductAction -> Product 11531743192 already has an active discount by cosmin.oprea","timestamp":"2025-06-05 09:04:15"}
{"level":"error","message":"DiscountAddProductAction -> Product \"11531743192\" already has a discount by cosmin.oprea","timestamp":"2025-06-05 09:40:30"}
{"level":"error","message":"DiscountAddProductAction -> Product \"11531743192\" already has a discount by cosmin.oprea","timestamp":"2025-06-05 10:10:46"}
{"level":"error","message":"getProductDetails -> Error at parsing: [\n  {\n    \"code\": \"too_big\",\n    \"maximum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-06-05 12:30:59"}
{"level":"error","message":"getProductDetails -> Error at parsing: [\n  {\n    \"code\": \"too_big\",\n    \"maximum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-06-05 12:31:13"}
{"level":"error","message":"ProductSearchAction -> Error at parsing: [\n  {\n    \"code\": \"too_big\",\n    \"maximum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-06-05 12:49:11"}
{"level":"error","message":"ProductSearchAction -> Error at parsing: [\n  {\n    \"code\": \"too_big\",\n    \"maximum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-06-05 12:49:24"}
{"level":"error","message":"ProductSearchAction -> Error at parsing: [\n  {\n    \"code\": \"too_big\",\n    \"maximum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-06-05 12:59:12"}
{"level":"error","message":"getProductDetails -> Error at parsing: [\n  {\n    \"code\": \"too_big\",\n    \"maximum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-06-05 13:35:24"}
{"level":"error","message":"getProductDetails -> Error at parsing: [\n  {\n    \"code\": \"too_big\",\n    \"maximum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-06-05 13:36:14"}
{"level":"error","message":"getProductDetails -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-06-05 13:36:18"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:02:25"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:02:25"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:10"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:10"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:10"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:10"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:14"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:14"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:14"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:14"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:14"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:14"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:15"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:15"}
{"level":"error","message":"User with ID create not found: [object Object]","timestamp":"2025-06-05 17:04:29"}
{"level":"error","message":"User with ID create not found: [object Object]","timestamp":"2025-06-05 17:04:29"}
