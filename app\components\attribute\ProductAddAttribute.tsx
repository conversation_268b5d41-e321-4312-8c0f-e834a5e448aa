'use client'

import { useTransition } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ProductAddAttributeAction } from "@/app/actions/actions"
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ProductAddAttributeFormValues, ProductAddAttributeSchema } from "@/app/zod/zod"
import { toast } from "sonner"

export default function ProductAddAttribute({ materialNumber }: { materialNumber: string }) {

    const [isLoading, startTransition] = useTransition()

    const {
        register,
        handleSubmit,
        formState: { errors, isSubmitting },
        reset,
    } = useForm<ProductAddAttributeFormValues>({
        resolver: zodResolver(ProductAddAttributeSchema),
        defaultValues: {materialNumber}
    });

    const onSubmit = async (data: ProductAddAttributeFormValues) => {
        startTransition(async () => {
            try {
                const response = await ProductAddAttributeAction(data);
                if(response.status === "SUCCESS"){
                    reset();
                    toast.success(response.message);
                }else{
                    toast.error(response.message)
                }  
            } catch (error) {
                console.error("Unexpected error:", error);
            }
        })
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)}  className="flex gap-2 mb-4">    
            <input type="hidden" {...register("materialNumber")} value={materialNumber} />
            
            <label className="block mb-1">Key:</label>
            <input
                type="text"
                {...register("key")}
                className="border rounded-lg w-200"
            />
            {errors.key && (
                <p className="text-red-500">{errors.key.message}</p>
            )}

            <label className="block mb-1">Value:</label>
            <input
                type="text"
                {...register("value")}
                className="border rounded-lg w-200"
            />
            {errors.value && (
                <p className="text-red-500">{errors.value.message}</p>
            )}

            <Button
                type="submit"
                disabled={isSubmitting || isLoading}
                variant="default"
            >
                {isSubmitting || isLoading ? "Adding..." : "Add Attribute"}
            </Button>

        </form>
    )
}




// "use client"

// import { Button } from "@/components/ui/button"
// import { Input } from "@/components/ui/input"
// import { TableCell, TableRow } from "@/components/ui/table"

// export default function ProductAddAttribute({ materialNumber }: {materialNumber: string}) {

//     return(
//         <>
//             <TableRow>
//                 <TableCell>{materialNumber}</TableCell>
//                 <TableCell><Input /></TableCell>
//                 <TableCell><Input /></TableCell>
//                 <TableCell><Button variant={"default"}>Add Attribute</Button></TableCell>
//             </TableRow>
//         </>
//     )
// }