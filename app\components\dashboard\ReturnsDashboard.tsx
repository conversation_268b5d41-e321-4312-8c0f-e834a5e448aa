import Link from "next/link";
import { getReturns } from "@/app/getData/return/data";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ReturnStatus } from "@/generated/prisma";
import { formatDate } from "@/app/utils/formatters";

export default async function ReturnsDashboard() {
  // Get the 5 most recent returns
  const recentReturns = await getReturns(5);
  
  // Count returns by status
  const requestedCount = await getReturns(100, ReturnStatus.requested);
  const pendingCount = requestedCount.length;
  
  // Helper function to get status badge color
  const getStatusColor = (status: ReturnStatus) => {
    switch (status) {
      case ReturnStatus.requested:
        return "bg-blue-100 text-blue-800";
      case ReturnStatus.approved:
        return "bg-green-100 text-green-800";
      case ReturnStatus.received:
        return "bg-purple-100 text-purple-800";
      // case ReturnStatus.processed:
      //   return "bg-indigo-100 text-indigo-800";
      // case ReturnStatus.refunded:
      //   return "bg-emerald-100 text-emerald-800";
      case ReturnStatus.rejected:
        return "bg-red-100 text-red-800";
      case ReturnStatus.cancelled:
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Returns</CardTitle>
        <CardDescription>
          Manage customer return requests
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-blue-800">Pending Returns</h3>
            <p className="text-2xl font-bold text-blue-900">{pendingCount}</p>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-gray-800">Total Returns</h3>
            <p className="text-2xl font-bold text-gray-900">{recentReturns.length}</p>
          </div>
        </div>
        
        <h3 className="text-sm font-medium mb-3">Recent Returns</h3>
        {recentReturns.length === 0 ? (
          <p className="text-gray-500 text-center py-4">No returns found</p>
        ) : (
          <div className="space-y-3">
            {recentReturns.map((returnItem) => (
              <div 
                key={returnItem.id} 
                className="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
              >
                <div>
                  <p className="font-medium">{returnItem.returnNumber}</p>
                  <p className="text-sm text-gray-500">
                    {formatDate(returnItem.createdAt)}
                  </p>
                </div>
                <Badge className={getStatusColor(returnItem.status)}>
                  {returnItem.status.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </Badge>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button asChild className="w-full">
          <Link href="/returns">View All Returns</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}