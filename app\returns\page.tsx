import Link from "next/link";
import { getReturns } from "@/app/getData/return/data";
import { formatDate } from "@/lib/utils";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { Button } from "@/components/ui/button";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { ReturnStatus } from "@prisma/client";

export const metadata = {
  title: "Returns | Parts Database",
  description: "Manage product returns",
};

export default async function ReturnsPage() {
  await requireAdminOrModerator();
  const returns = await getReturns();

  // Helper function to get status badge color
  const getStatusColor = (status: ReturnStatus) => {
    switch (status) {
      case ReturnStatus.requested:
        return "bg-blue-100 text-blue-800";
      case ReturnStatus.approved:
        return "bg-green-100 text-green-800";
      case ReturnStatus.received:
        return "bg-purple-100 text-purple-800";
      case ReturnStatus.processed:
        return "bg-indigo-100 text-indigo-800";
      case ReturnStatus.refunded:
        return "bg-emerald-100 text-emerald-800";
      case ReturnStatus.rejected:
        return "bg-red-100 text-red-800";
      case ReturnStatus.cancelled:
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Returns</h1>
      </div>

      {returns.length === 0 ? (
        <div className="text-center py-12">
          <h2 className="text-xl font-medium text-gray-600 mb-4">No returns found</h2>
          <p className="text-gray-500 mb-6">There are no return requests in the system yet.</p>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Return #</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Items</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {returns.map((returnItem) => (
                <TableRow key={returnItem.id}>
                  <TableCell className="font-medium">
                    {returnItem.returnNumber}
                  </TableCell>
                  <TableCell>{formatDate(returnItem.createdAt)}</TableCell>
                  <TableCell>
                    {returnItem.order?.user?.firstName} {returnItem.order?.user?.lastName}
                  </TableCell>
                  <TableCell>{returnItem.returnItems.length} items</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(returnItem.status)}>
                      {returnItem.status.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <Button asChild variant="outline" size="sm">
                      <Link href={`/returns/${returnItem.id}`}>
                        View Details
                      </Link>
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
}