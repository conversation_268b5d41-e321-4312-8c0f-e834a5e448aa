import { Metadata } from "next";
import { notFound } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { getBannerById } from "@/app/getData/banner/getBanners";
import BannerForm from "@/app/components/banner/BannerForm";
import { requireAdminOrModerator } from "@/lib/auth-utils";

export const metadata: Metadata = {  title: "Edit Banner",
  description: "Edit an existing banner",
};

interface EditBannerPageProps {
  params: Promise<{ id: string }>;
}
export default async function EditBannerPage({ params }: EditBannerPageProps) {
  await requireAdminOrModerator();
  const paramsObject = await params;
  const idAsString = paramsObject.id;    //All params are strings and need to be parsed
  const banner = await getBannerById(idAsString);
  
  if (!banner) {    notFound();
  }  
  return (    <div className="container mx-auto py-8">
      <div className="flex items-center gap-4 mb-6">        <Link href="/banner">
          <Button variant="outline">Back to Banners</Button>        </Link>
        <h1 className="text-3xl font-bold">Edit Banner</h1>      </div>
            <BannerForm
        banner={banner}         isEditing={true} 
      />    </div>
  );
}





















