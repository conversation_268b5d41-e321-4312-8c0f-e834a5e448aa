import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import DiscountTableWithProducts from "@/app/components/discount/DiscountTableWithProducts";
import { DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import { getProductsForDiscountWithDetails } from "@/app/getData/discount/data";
import DiscountAddProductForm from "@/app/components/discount/DiscountAddProductForm";
import AddProductsFromCSV from "@/app/components/discount/DiscountAddProductsCSV";
import DiscountDeleteAllProducts from "@/app/components/discount/DiscountDeleteAllProducts";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { notFound } from "next/navigation";

export default async function DiscountRoute({ params }: { params: Promise<{ id: string }> }) {
  await requireAdminOrModerator();
  const paramsObject = await params;
  const idAsString = paramsObject.id;    //All params are strings and need to be parsed

  const result = await getProductsForDiscountWithDetails(idAsString);
  
  if (!result) {
    return notFound();
  }

  if (!result.discount) {
    return notFound();
  }
  
  const { discount, products } = result;

  return (
<>
        <h2 className="text-2xl font-bold mb-4">Products for `{discount.name}`</h2>
        <Link href="/discounts">
            <Button variant="outline">Go back</Button>
        </Link>
          <div className="my-4 space-y-4">
              <h2 className="text-lg font-bold">Add Product</h2>
              <DiscountAddProductForm discountId={idAsString} />
          </div>

          <DropdownMenuSeparator />

          <div className="mb-6 space-y-4">
            <h2 className="text-lg font-bold">Upload Products with CSV </h2><span className="text-foreground text-xs">Must contain a header : material_number</span>
            <AddProductsFromCSV discountId={idAsString} />
          </div>

          <DropdownMenuSeparator />

          <div className="mb-6 space-y-4">
            <h2 className="text-lg font-bold">Delete All Products</h2>
            <DiscountDeleteAllProducts discountId={idAsString} />
          </div>

        {
          !products.length 
          ?<>There are no products</>
          : 
          <DiscountTableWithProducts products={products}  discount={discount} />
        }
</>
  );
}
