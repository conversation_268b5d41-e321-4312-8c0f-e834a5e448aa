import { But<PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import ProductAddAttribute from "@/app/components/attribute/ProductAddAttribute";
import { getProductAttributes, getProductAttributesHistory } from "@/app/getData/product/data";
import Link from "next/link";
import ProductDeleteAttribute from "@/app/components/attribute/ProductDeleteAttribute";
import ProductAttributeHistoryPage from "@/app/components/attribute/ProductAttributeHistory";
import { requireAdminOrModerator } from "@/lib/auth-utils";

export default async function AttributesRoute({ params }: { params: Promise<{ material_number: string }> }) {
    await requireAdminOrModerator();
    const paramsObject = await params;
    const materialNumber = paramsObject.material_number;    //All params are strings and need to be parsed
    const productAttribute = await getProductAttributes(paramsObject.material_number)

    const materialNumberAttributesHistory = await getProductAttributesHistory(materialNumber)

    return (
      <>
        {/* Display attributes */}
        <Card className="mb-4">
          <CardHeader>
            <CardTitle>
              Attributes <br></br><br></br>
              <Link href="/product"><Button variant="outline" className="">Go back</Button></Link>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {productAttribute.length > 0 
              ? 
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Material Number</TableHead>
                      <TableHead>Key</TableHead>
                      <TableHead>Value</TableHead>
                      <TableHead>Delete</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {productAttribute
                      .filter(i => i.key !== null) // Filter out null keys
                      .map((i, k) => (
                        <TableRow key={k}>
                          <TableCell>{materialNumber}</TableCell>
                          <TableCell>{i.key}</TableCell>
                          <TableCell>{i.value}</TableCell>
                          <TableCell>
                            <ProductDeleteAttribute materialNumber={materialNumber} attributeKey={i.key!} />
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              : <p>There are no attributes associated.</p>
            }
          </CardContent>
        </Card>

        {/* Form for adding attribute */}
        <Card className="mb-4">
          <CardHeader><CardTitle>Add New Attribute for `{materialNumber}`</CardTitle></CardHeader>
          <CardContent>
            <ProductAddAttribute materialNumber={materialNumber} />
          </CardContent>
        </Card>


        {/* Display Attributes History*/}
        <Card className="mb-4">
          <CardHeader><CardTitle>Attribute History for `{materialNumber}`</CardTitle></CardHeader>
          <CardContent>
            <ProductAttributeHistoryPage materialNumberAttributesHistory={materialNumberAttributesHistory} />
          </CardContent>
        </Card>
        </>
    )
}
