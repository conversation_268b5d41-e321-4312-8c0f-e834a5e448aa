// app/search/page.tsx
// This is a Server Component by default

import { Card } from "@/components/ui/card";
import { CategorySearchForm } from "../components/categories/CategorySearchForm";
import { getCategoriesByFamilyCode } from "../getData/categories/getData";
import { CategoryFetchResult } from "../types/Types";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import CategoryUploadCSV from "../components/categories/CategoryUploadCSV";

// Define the type for the searchParams prop that Next.js automatically provides
interface SearchPageProps {
  searchParams: Promise<{
    familyCode?: string; // The awaited object will have this structure
  }>;
}

export default async function CategoriesRoute(props: SearchPageProps) {

  await requireAdminOrModerator();

  const searchParams = await props.searchParams;

  const initialFamilyCode = searchParams.familyCode;
  
  const fetchResult: CategoryFetchResult = await getCategoriesByFamilyCode(initialFamilyCode)

  const categories = fetchResult.categories;
  const message = fetchResult.message;
  const errors = fetchResult.errors;

  return (
      <div className="container mx-auto py-8">
      <Card className="p-4 mb-10">
        <CategorySearchForm
          initialFamilyCode={initialFamilyCode}
          serverErrors={errors?.familyCode}
        />

        {/* Display the search message from the server function */}
        {message && (
          <p className={`ml-2
            ${categories && categories.length > 0
              ? 'text-green-600' // Success
              : (errors?.familyCode
                ? 'text-red-600' // Validation error
                : 'text-gray-700') // General prompt or no results
            }`}>
            {message}
          </p>
        )}

        {/* Display found categories */}
        {categories && categories.length > 0 && (
          <div className="w-full">
            <div className="space-y-4">
              {categories.map((category) => (
                <div key={category.level3.id} className="p-3 border border-gray-300 rounded-md bg-white shadow-sm">
                  <p>
                    <strong>Level 1:</strong> {category.level1.name} ({category.level1.nameRO || 'N/A'})
                    {category.level1.imageUrl && <img src={category.level1.imageUrl} alt={category.level1.name} className="inline-block h-6 w-6 ml-2" />}
                  </p>
                  <p className="ml-4">
                    <strong>Level 2:</strong> {category.level2.name} ({category.level2.nameRO || 'N/A'})
                    {category.level2.imageUrl && <img src={category.level2.imageUrl} alt={category.level2.name} className="inline-block h-6 w-6 ml-2" />}
                  </p>
                  <p className="ml-6">
                    <strong>Level 3:</strong> {category.level3.name} ({category.level3.nameRO || 'N/A'})
                    {category.level3.imageUrl && <img src={category.level3.imageUrl} alt={category.level3.name} className="inline-block h-6 w-6 ml-2" />}
                  </p>
                  <p className="ml-8">
                    <strong>Family Code:</strong> {category.level3.familyCode} has 123 products
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}
      </Card>
      
      {/* Add the CSV upload component */}
      <CategoryUploadCSV />
    </div>
  );
}



