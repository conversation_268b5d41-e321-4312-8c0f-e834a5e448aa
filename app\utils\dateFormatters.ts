// import { format, formatDistanceToNow, FormatDistanceOptions } from "date-fns";
// import { enUS } from "date-fns/locale";

// // Always use enUS locale for consistency between server and client
// const dateLocale = enUS;

// // Format a date to a standard string format
// export function formatDate(date: Date | string | number): string {
//   const dateObj = date instanceof Date ? date : new Date(date);
//   return format(dateObj, 'MM/dd/yyyy, h:mm:ss a', { locale: dateLocale });
// }

// // Format a date as a relative time (e.g., "2 days ago")
// export function formatRelativeTime(date: Date | string | number, options?: Omit<FormatDistanceOptions, 'locale'>): string {
//   const dateObj = date instanceof Date ? date : new Date(date);
//   return formatDistanceToNow(dateObj, { 
//     addSuffix: true, 
//     locale: dateLocale,
//     ...options
//   });
// }