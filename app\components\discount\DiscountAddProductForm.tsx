"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState, useTransition } from "react";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { ServerError } from "@/app/types/Types";
import { DiscountAddProductSchema, DiscountAddProductToFormValues } from "@/app/zod/zod";
import { DiscountAddProductAction } from "@/app/actions/actions";


interface AddProductFormProps {
  discountId: string;
}

export default function DiscountAddProductForm({ discountId }: AddProductFormProps) {
  const [isLoading, startTransition] = useTransition()
  const [serverError, setServerError] = useState<ServerError | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
    reset,
  } = useForm<DiscountAddProductToFormValues>({
    resolver: zodResolver(DiscountAddProductSchema),
    defaultValues: { discountId },
  });

  const onSubmit = async (data: DiscountAddProductToFormValues) => {
    setServerError(null); // Clear previous server error

    startTransition(async () => {
      try {
        const response = await DiscountAddProductAction(data);
        if(response.status === "SUCCESS"){
          reset();
          toast.success(response.message);
        }else{
          toast.error(response.message)

          if (response.fieldErrors) {
            Object.entries(response.fieldErrors).forEach(([field, message]) => {
              setError(field as keyof DiscountAddProductToFormValues, {
                type: "server",
                message,
              });
            });
          } else {
            setServerError({ message: String(response.message) });
          }
        }  
      } catch (error) {
          console.error("Unexpected error:", error);
          setServerError({ message: "Unexpected error occurred. Please try again later." });
      }
    })
  };

  return (
    <>
    <form onSubmit={handleSubmit(onSubmit)} className="">      
      <div className="flex space-x-4">
      <input type="hidden" {...register("discountId")} value={discountId} />


        <label className="block mb-1">Material Number:</label>
        <input
          type="text"
          {...register("materialNumber")}
          className="border rounded-lg"
        />
        {errors.materialNumber && (
          <p className="text-red-500">{errors.materialNumber.message}</p>
        )}
      
      <Button
        type="submit"
        disabled={isSubmitting || isLoading}
        variant="default"
        className=""
      >
        {isSubmitting || isLoading ? "Adding..." : "Add Product"}
      </Button>
      </div>
    </form>
        {serverError && (
          <p className="text-red-500 mb-2">{serverError.message}</p>
        )}
    </>
  );
}
