import { <PERSON><PERSON><PERSON> } from "next";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import BannerForm from "@/app/components/banner/BannerForm";
import { requireAdminOrModerator } from "@/lib/auth-utils";

export const metadata: Metadata = {
  title: "Create Banner",
  description: "Create a new banner for your website",
};

export default async function CreateBannerPage() {
  await requireAdminOrModerator();
  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center gap-4 mb-6">
        <Link href="/banner">
          <Button variant="outline">Back to Banners</Button>
        </Link>
        <h1 className="text-3xl font-bold">Create New Banner</h1>
      </div>
      
      <BannerForm isEditing={false} />
    </div>
  );
}