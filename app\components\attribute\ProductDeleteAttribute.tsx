"use client";

import { useTransition } from "react";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { toast } from "sonner";

import clientLogger from "@/lib/clientLogger";
import { ProductDeleteAttributeAction } from "@/app/actions/actions";

interface ProductListProps {
  materialNumber: string;
  attributeKey: string;
}

export default function ProductDeleteAttribute({ materialNumber, attributeKey }: ProductListProps) {
  console.log(attributeKey)
  const [isPending, startTransition] = useTransition();

  const handleDelete = () => {

    startTransition(async () => {

      try {
        const response = await ProductDeleteAttributeAction({ materialNumber, attributeKey });
         if(response.status === "SUCCESS"){
          toast.success(response.message);
         }else{
          toast.error(response.message);
          clientLogger.error(`Client-Side-> ProductDeleteAttribute.tsx `)
        }
      } catch (error) {
        toast.error("An unexpected error occured.")
          clientLogger.error(`Unexpected error: ${error}`);
      }
    });
  };

  return (
     <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button variant="destructive">Delete</Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete the product from the discount.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction 
            className="bg-red-500"
            onClick={() => handleDelete()}
            disabled={isPending}
            >{isPending ? "Deleting..." : "Delete"}</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
