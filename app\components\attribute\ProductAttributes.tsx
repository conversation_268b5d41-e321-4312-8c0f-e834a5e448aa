// "use client"

// import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
// import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
// import ProductAddAttribute from '../attribute/ProductAddAttribute';

// interface FoundProductAttributeInterface{
//     foundProductAttribute: {
//         Material_Number: string;
//         key: string | null;
//         value: string | null;
//         id: number;
//     }[]
// }

// export default function ProductAttributesPage({ foundProductAttribute }: FoundProductAttributeInterface) {
//     return (
//         <Card className="mb-4">
//           <CardHeader>
//             <CardTitle>Attributes</CardTitle> 
//           </CardHeader>
//           <CardContent>
//             <Table>
//               <TableHeader>
//                 <TableRow>
//                   <TableHead>Material Number</TableHead>
//                   <TableHead>Key</TableHead>
//                   <TableHead>Value</TableHead>
//                   <TableHead>Delete</TableHead>
//                 </TableRow>
//               </TableHeader>
//               <TableBody>
//                 {foundProductAttribute.map((i,k) => (
//                   <TableRow key={k}>
//                     <TableCell>{i.Material_Number}</TableCell>
//                     <TableCell>{i.key}</TableCell>
//                     <TableCell>{i.value}</TableCell>
//                     <TableCell>
//                       {/* Form for deleting attribute */}
//                       {/* <ProductDeleteAttribute materialNumber={i.Material_Number} key={i.key ?? `${i.Material_Number}-${k}`} /> */}
//                     </TableCell>
//                   </TableRow>
//                 ))}
//               </TableBody>
//             </Table>
         
//             {/* Form for adding attribute */}
//             <ProductAddAttribute materialNumber={foundProductAttribute[0].Material_Number} />
         
//           </CardContent>
//         </Card>
//     )
// }