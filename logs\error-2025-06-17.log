{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 10:32:23"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 10:32:29"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:24:21"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:29:13"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:29:26"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 11:29:29"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:29:31"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:29:52"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:31:01"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 11:31:03"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:31:04"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 11:31:06"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:31:07"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 11:31:10"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:31:11"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:31:25"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 11:31:37"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:31:39"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 11:31:39"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:31:40"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 11:31:41"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:31:42"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 11:31:55"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:32:28"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:33:07"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:34:53"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 11:38:31"}
{"level":"error","message":"getOrderById -> Error at parsing: [\n  {\n    \"validation\": \"cuid\",\n    \"code\": \"invalid_string\",\n    \"message\": \"Invalid cuid\",\n    \"path\": []\n  }\n] by <EMAIL>","timestamp":"2025-06-17 11:38:46"}
{"level":"error","message":"getOrderById -> Error at parsing: [\n  {\n    \"validation\": \"cuid\",\n    \"code\": \"invalid_string\",\n    \"message\": \"Invalid cuid\",\n    \"path\": []\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:07:51"}
{"level":"error","message":"getOrderById -> Error at parsing: [\n  {\n    \"validation\": \"cuid\",\n    \"code\": \"invalid_string\",\n    \"message\": \"Invalid cuid\",\n    \"path\": []\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:07:51"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:08:50"}
{"level":"error","message":"getOrderById -> Error at parsing: [\n  {\n    \"validation\": \"cuid\",\n    \"code\": \"invalid_string\",\n    \"message\": \"Invalid cuid\",\n    \"path\": []\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:09:02"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:10:12"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:11:33"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:12:08"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:19:19"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:19:19"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:19:24"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:22:34"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 12:25:56"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:25:57"}
