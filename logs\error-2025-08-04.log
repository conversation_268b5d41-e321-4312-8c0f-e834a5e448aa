{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-04 13:53:01"}
{"level":"error","message":"Error fetching user with ID cmdwxot5n000shx0of0ymzh6z:: [object Object]","timestamp":"2025-08-04 15:26:50"}
{"level":"error","message":"Error fetching user with ID cmdwxot5n000shx0of0ymzh6z:: [object Object]","timestamp":"2025-08-04 15:26:59"}
{"level":"error","message":"Error fetching user with ID cmdwxot5n000shx0of0ymzh6z:: [object Object]","timestamp":"2025-08-04 15:27:07"}
