"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { formatOrderStatus } from "@/app/utils/formatters";

export default function OrderStatusUpdate({ 
  orderId, 
  currentStatus 
}: { 
  orderId: string; 
  currentStatus: string;
}) {
  const router = useRouter();
  const [status, setStatus] = useState(currentStatus);
  const [note, setNote] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const statusOptions = [
    { value: "plasata", label: "Placed" },
    { value: "procesata", label: "Processing" },
    { value: "expediata", label: "Shipped" },
    { value: "livrata", label: "Delivered" },
    { value: "anulata", label: "Cancelled" }
  ];

  const handleSubmit = async () => {
    if (status === currentStatus) return;
    
    setIsSubmitting(true);
    
    try {
      const response = await fetch(`/api/orders/${orderId}/status`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status,
          note: note.trim() || undefined,
        }),
      });
      
      if (!response.ok) {
        throw new Error("Failed to update order status");
      }
      
      router.refresh();
    } catch (error) {
      console.error("Error updating order status:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-1">
          Update Status
        </label>
        <Select value={status} onValueChange={setStatus}>
          <SelectTrigger>
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            {statusOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      <div>
        <label className="block text-sm font-medium mb-1">
          Note (optional)
        </label>
        <Textarea
          placeholder="Add a note about this status change"
          value={note}
          onChange={(e) => setNote(e.target.value)}
          rows={3}
        />
      </div>
      
      <Button 
        onClick={handleSubmit} 
        disabled={status === currentStatus || isSubmitting}
        className="w-full"
      >
        {isSubmitting ? "Updating..." : "Update Status"}
      </Button>
    </div>
  );
}