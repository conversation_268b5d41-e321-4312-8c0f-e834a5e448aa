{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 08:08:00"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 10:31:36"}
{"level":"info","message":"getProductAttributes -> Product \"11531743192\" has the attributes:  by <EMAIL>","timestamp":"2025-06-17 10:31:49"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11531743192\" with <NAME_EMAIL>","timestamp":"2025-06-17 10:31:49"}
{"level":"info","message":"ProductAddAttributeAction -> Attribute \"color\" added successfully for product 11531743192 by <EMAIL>","timestamp":"2025-06-17 10:31:54"}
{"level":"info","message":"getProductAttributes -> Product \"11531743192\" has the attributes: color -- <NAME_EMAIL>","timestamp":"2025-06-17 10:31:54"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11531743192\" with <NAME_EMAIL>","timestamp":"2025-06-17 10:31:54"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 10:32:23"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 10:32:29"}
{"level":"info","message":"DiscountUpdateAction -> Discount with name Summer tyres updated <NAME_EMAIL>","timestamp":"2025-06-17 10:32:53"}
{"level":"info","message":"Starting activate discount check at 2025-06-17T07:32:58.757<NAME_EMAIL>","timestamp":"2025-06-17 10:32:58"}
{"level":"info","message":"Found 1 discounts to <NAME_EMAIL>","timestamp":"2025-06-17 10:32:58"}
{"level":"info","message":"Activated discount cmbuqdkl00003hxv4vm6utec5: activated and updated 0 products","timestamp":"2025-06-17 10:32:58"}
{"level":"info","message":"Starting expired discount check at 2025-06-17T07:33:01.965<NAME_EMAIL>","timestamp":"2025-06-17 10:33:01"}
{"level":"info","message":"Found 0 expired discounts to <NAME_EMAIL>","timestamp":"2025-06-17 10:33:01"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 10:34:07"}
{"level":"info","message":"Product 11121740428 added to featured <NAME_EMAIL>","timestamp":"2025-06-17 10:34:44"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 10:34:44"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 10:35:32"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 10:35:46"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 10:35:46"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 10:35:47"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 10:35:47"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 10:35:47"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 10:35:47"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 10:35:48"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 10:35:48"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 10:35:48"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 10:35:48"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 10:35:48"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 10:35:48"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 10:35:49"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 10:35:49"}
{"level":"info","message":"Banner updated successfully: cmbyp3o9r0000hx6smiv3<NAME_EMAIL>","timestamp":"2025-06-17 10:36:31"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 10:36:40"}
{"level":"info","message":"User status changed for user cmbuptw1z0000hxv4bz6vufe1 with changes: {\"isActive\":true,\"isSuspended\":true,\"suspensionReason\":\"Administrative action\"} by <EMAIL>","timestamp":"2025-06-17 10:57:38"}
{"level":"info","message":"User status changed for user cmbuptw1z0000hxv4bz6vufe1 with changes: {\"isActive\":true,\"isSuspended\":false,\"suspensionReason\":\"\"} by <EMAIL>","timestamp":"2025-06-17 10:57:43"}
{"level":"info","message":"User status changed for user cmbuptw1z0000hxv4bz6vufe1 with changes: {\"isActive\":false,\"isSuspended\":false,\"suspensionReason\":\"\"} by <EMAIL>","timestamp":"2025-06-17 10:59:10"}
{"level":"info","message":"User status changed for user cmbuptw1z0000hxv4bz6vufe1 with changes: {\"isActive\":true,\"isSuspended\":false,\"suspensionReason\":\"\"} by <EMAIL>","timestamp":"2025-06-17 10:59:17"}
{"level":"info","message":"User sync <NAME_EMAIL>","timestamp":"2025-06-17 11:07:28"}
{"level":"info","message":"User sync <NAME_EMAIL>: 2 users processed, 0 new, 2 updated","timestamp":"2025-06-17 11:14:13"}
{"level":"info","message":"User cmbuozlcs0000hxnccvllju43 details retrieved for userEmail","timestamp":"2025-06-17 11:14:28"}
{"level":"info","message":"User cmbuozlcs0000hxnccvllju43 details retrieved for userEmail","timestamp":"2025-06-17 11:15:04"}
{"level":"info","message":"User cmbuozlcs0000hxnccvllju43 details retrieved for userEmail","timestamp":"2025-06-17 11:15:07"}
{"level":"info","message":"User cmbuptw1z0000hxv4bz6vufe1 details retrieved for userEmail","timestamp":"2025-06-17 11:16:02"}
{"level":"info","message":"User cmbuptw1z0000hxv4bz6vufe1 details retrieved for userEmail","timestamp":"2025-06-17 11:16:21"}
{"level":"info","message":"User cmbuptw1z0000hxv4bz6vufe1 details retrieved for userEmail","timestamp":"2025-06-17 11:16:23"}
{"level":"info","message":"User cmbuptw1z0000hxv4bz6vufe1 details retrieved for userEmail","timestamp":"2025-06-17 11:21:49"}
{"level":"info","message":"User cmbuptw1z0000hxv4bz6vufe1 details retrieved for userEmail","timestamp":"2025-06-17 11:21:58"}
{"level":"info","message":"User updated successfully: cosmin.oprea@automobilebavaria.<NAME_EMAIL> with changes: {\"role\":{\"from\":\"inregistratAB\",\"to\":\"moderatorAB\"}}","timestamp":"2025-06-17 11:22:16"}
{"level":"info","message":"User cmbuptw1z0000hxv4bz6vufe1 details retrieved for userEmail","timestamp":"2025-06-17 11:22:16"}
{"level":"info","message":"User cmbuptw1z0000hxv4bz6vufe1 details retrieved for userEmail","timestamp":"2025-06-17 11:22:17"}
{"level":"info","message":"User cmbuptw1z0000hxv4bz6vufe1 details retrieved for userEmail","timestamp":"2025-06-17 11:22:26"}
{"level":"info","message":"User cmbuozlcs0000hxnccvllju43 details retrieved for userEmail","timestamp":"2025-06-17 11:23:32"}
{"level":"info","message":"User updated successfully: oprea.cosmin.adrian@gmail.<NAME_EMAIL> with changes: {\"role\":{\"from\":\"moderatorAB\",\"to\":\"administAB\"}}","timestamp":"2025-06-17 11:23:44"}
{"level":"info","message":"User cmbuozlcs0000hxnccvllju43 details retrieved for userEmail","timestamp":"2025-06-17 11:23:44"}
{"level":"info","message":"User cmbuozlcs0000hxnccvllju43 details retrieved for userEmail","timestamp":"2025-06-17 11:23:44"}
{"level":"info","message":"User status changed for user cmbuptw1z0000hxv4bz6vufe1 with changes: {\"isActive\":false,\"isSuspended\":false,\"suspensionReason\":\"\"} by <EMAIL>","timestamp":"2025-06-17 11:24:05"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:24:21"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:29:13"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:29:26"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 11:29:29"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:29:31"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:29:52"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:31:01"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 11:31:03"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:31:04"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 11:31:06"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:31:07"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 11:31:10"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:31:11"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:31:25"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 11:31:37"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:31:39"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 11:31:39"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:31:40"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 11:31:41"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:31:42"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 11:31:51"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 11:31:55"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:32:28"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:33:07"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 11:34:53"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 11:38:31"}
{"level":"error","message":"getOrderById -> Error at parsing: [\n  {\n    \"validation\": \"cuid\",\n    \"code\": \"invalid_string\",\n    \"message\": \"Invalid cuid\",\n    \"path\": []\n  }\n] by <EMAIL>","timestamp":"2025-06-17 11:38:46"}
{"level":"error","message":"getOrderById -> Error at parsing: [\n  {\n    \"validation\": \"cuid\",\n    \"code\": \"invalid_string\",\n    \"message\": \"Invalid cuid\",\n    \"path\": []\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:07:51"}
{"level":"error","message":"getOrderById -> Error at parsing: [\n  {\n    \"validation\": \"cuid\",\n    \"code\": \"invalid_string\",\n    \"message\": \"Invalid cuid\",\n    \"path\": []\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:07:51"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:08:50"}
{"level":"error","message":"getOrderById -> Error at parsing: [\n  {\n    \"validation\": \"cuid\",\n    \"code\": \"invalid_string\",\n    \"message\": \"Invalid cuid\",\n    \"path\": []\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:09:02"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:10:12"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:11:33"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:12:08"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:19:19"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:19:19"}
{"level":"info","message":"Created test order cmc0bai5c0005hxpo2j6ynk6c with order number TEST-963998","timestamp":"2025-06-17 12:19:24"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:19:24"}
{"level":"info","message":"Added 3 test products to order cmc0bai5c0005hxpo2j6ynk6c by userEmail","timestamp":"2025-06-17 12:21:32"}
{"level":"info","message":"User cmbuozlcs0000hxnccvllju43 details retrieved for userEmail","timestamp":"2025-06-17 12:21:59"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:22:34"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-06-17 12:25:56"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-06-17 12:25:57"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 12:25:59"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-06-17 12:26:01"}
{"level":"info","message":"User status changed for user cmbuptw1z0000hxv4bz6vufe1 with changes: {\"isActive\":true,\"isSuspended\":true,\"suspensionReason\":\"Administrative action\"} by <EMAIL>","timestamp":"2025-06-17 12:26:12"}
{"level":"info","message":"User status changed for user cmbuptw1z0000hxv4bz6vufe1 with changes: {\"isActive\":true,\"isSuspended\":false,\"suspensionReason\":\"\"} by <EMAIL>","timestamp":"2025-06-17 12:26:54"}
{"level":"info","message":"User cmbuptw1z0000hxv4bz6vufe1 details retrieved for userEmail","timestamp":"2025-06-17 12:30:06"}
{"level":"info","message":"User cmbuozlcs0000hxnccvllju43 details retrieved for userEmail","timestamp":"2025-06-17 12:30:10"}
{"level":"info","message":"User cmbuptw1z0000hxv4bz6vufe1 details retrieved for userEmail","timestamp":"2025-06-17 12:30:19"}
{"level":"info","message":"User cmbuozlcs0000hxnccvllju43 details retrieved for userEmail","timestamp":"2025-06-17 12:30:29"}
{"level":"info","message":"User cmbuozlcs0000hxnccvllju43 details retrieved for userEmail","timestamp":"2025-06-17 12:35:48"}
{"level":"info","message":"User cmbuozlcs0000hxnccvllju43 details retrieved for userEmail","timestamp":"2025-06-17 12:36:17"}
{"level":"info","message":"User cmbuozlcs0000hxnccvllju43 details retrieved for userEmail","timestamp":"2025-06-17 12:36:47"}
{"level":"info","message":"User cmbuozlcs0000hxnccvllju43 details retrieved for userEmail","timestamp":"2025-06-17 12:36:51"}
{"level":"info","message":"User cmbuozlcs0000hxnccvllju43 details retrieved for userEmail","timestamp":"2025-06-17 12:37:31"}
{"level":"info","message":"User cmbuozlcs0000hxnccvllju43 details retrieved for userEmail","timestamp":"2025-06-17 12:37:41"}
{"level":"info","message":"User cmbuptw1z0000hxv4bz6vufe1 details retrieved for userEmail","timestamp":"2025-06-17 12:38:00"}
{"level":"info","message":"User cmbuozlcs0000hxnccvllju43 details retrieved for userEmail","timestamp":"2025-06-17 12:38:04"}
{"level":"info","message":"User cmbuozlcs0000hxnccvllju43 details retrieved for userEmail","timestamp":"2025-06-17 12:38:42"}
{"level":"info","message":"User cmbuozlcs0000hxnccvllju43 details retrieved for userEmail","timestamp":"2025-06-17 12:39:16"}
{"level":"info","message":"User cmbuptw1z0000hxv4bz6vufe1 details retrieved for userEmail","timestamp":"2025-06-17 12:39:47"}
{"level":"info","message":"User sync <NAME_EMAIL>: 2 users processed, 0 new, 2 updated","timestamp":"2025-06-17 12:40:20"}
{"level":"info","message":"User sync <NAME_EMAIL>: 2 users processed, 0 new, 2 updated","timestamp":"2025-06-17 12:40:27"}
{"level":"info","message":"User cmbuptw1z0000hxv4bz6vufe1 details retrieved for userEmail","timestamp":"2025-06-17 12:43:28"}
