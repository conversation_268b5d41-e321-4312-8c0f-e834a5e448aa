import prisma from "@/app/utils/db";
import { logError } from "@/lib/logger";
import { getCurrentUser } from "../user/data";
import { z } from "zod";

export async function getProductsForDiscountWithDetails(discountId: string) {
  const user = await getCurrentUser()
  const userEmail = user?.email

  try{
    //validate parameter with zod
    const parsed = z.string().cuid().safeParse(discountId);
    if (!parsed.success) {
      logError(`getProductsForDiscountWithDetails -> Error at parsing: ${parsed.error} by ${userEmail}`)
      return {discount: null, products: []}
    }

    discountId = parsed.data


  if (!discountId) {
    logError(`getProductsForDiscountWithDetails -> Invalid discount ${discountId} for ${userEmail}`)
    return {discount: null, products: []}
  }

  // Fetch discount details
  const discount = await prisma.discount.findUnique({
    where: {
      id: discountId,
    },
    select: {
      id:true,
      name: true,
      type: true,
      value: true,
    },
  });

  if (!discount) {
    logError(`getProductsForDiscountWithDetails-> no discount found with id: ${discountId} for ${userEmail}`)
    return {discount: null, products: []}
  }

  // Fetch associated products
  const products = await prisma.productDiscount.findMany({
    where: {
      discountId,
    },
    select: {
      id: true,
      discountId: true,
      productId: true,
      createdAt: true,
      updatedAt: true,
      product: {
        select: {
          id: true,
          createdAt: true,
          Material_Number: true,
          Material_Group: true,
          Net_Weight: true,
          Description_Local: true,
          PretAM: true,
          last_updated_at: true,
          FinalPrice: true,
          HasDiscount: true,
          activeDiscountType: true,
          activeDiscountValue: true,
          discountPercentage: true,
        },
      },
    },
  });

  if (!products) {
    logError(`getProductsForDiscountWithDetails-> no products found for discount id: ${discountId} for ${userEmail}`)
    return {discount: null, products: []}
  }

  return {
    discount,
    products,
  };
  }catch(e){
    logError(`getProductsForDiscountWithDetails -> Unexpected error: ${e} for ${userEmail}`)
    return {discount: null, products: []}
  }
}

export async function getDiscountHistory(discountId: string){
  const user = await getCurrentUser()
  const userEmail = user?.email
  try{
    //validate parameter with zod
    const parsed = z.string().cuid().safeParse(discountId);
    if (!parsed.success) {
      logError(`getDiscountHistory -> Error at parsing: ${parsed.error} by ${userEmail}`)
      return []
    }

  const discountHistory = await prisma.discountHistory.findMany({
    where: {
      discountId: parsed.data, // ✅ filter by related discount ID
    },
    orderBy: {
      updatedAt: 'asc', // optional: show newest changes first
    },
    include: {
      discount: true, // include the related discount if needed
    },
  })

  if(!discountHistory){
    logError(`getDiscountHistory-> no discount history found with id: ${discountId} for ${userEmail}`)
    return []
  }

  return discountHistory
  }catch(e){
    logError(`getDiscountHistory-> Unexpected error: ${e} for ${userEmail}`)
    return []
  }
}