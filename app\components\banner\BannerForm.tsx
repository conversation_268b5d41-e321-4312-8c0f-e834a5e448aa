"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { createBanner, updateBanner } from "@/app/actions/bannerActions";
import { CreateBannerFormValues, UpdateBannerFormValues, createBannerSchema, updateBannerSchema } from "@/app/zod/zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent } from "@/components/ui/card";
import { Banner, BannerPlacement, DeviceTarget } from "@/generated/prisma";
import { z } from "zod";

interface BannerFormProps {
  banner?: Banner;
  isEditing?: boolean;
}

export default function BannerForm({ banner, isEditing = false }: BannerFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [previewUrl, setPreviewUrl] = useState(banner?.imageUrl || "");

  enum TextAlignment {
    LEFT = "LEFT",
    CENTER = "CENTER",
    RIGHT = "RIGHT",
  }

  const form = useForm<CreateBannerFormValues | UpdateBannerFormValues>({
    resolver: zodResolver(
      isEditing ? updateBannerSchema as z.ZodType<any> : createBannerSchema
    ),
    defaultValues: banner ? {
      id: banner.id, // Make sure ID is included for editing
      title: banner.title,
      subtitle: banner.subtitle || "",
      imageUrl: banner.imageUrl,
      mobileImageUrl: banner.mobileImageUrl || "",
      callToAction: banner.callToAction || "",
      buttonText: banner.buttonText || "",
      description: banner.description || "",
      url: banner.url || "",
      placement: banner.placement as BannerPlacement,
      position: banner.position,
      width: banner.width || "",
      height: banner.height || "",
      backgroundColor: banner.backgroundColor || "",
      textColor: banner.textColor || "",
      deviceTarget: banner.deviceTarget as DeviceTarget,
      startDate: banner.startDate,
      endDate: banner.endDate || undefined,
      isActive: banner.isActive,
      textAlignment: banner.textAlignment as TextAlignment,
    } : {
      title: "",
      subtitle: "",
      imageUrl: "",
      mobileImageUrl: "",
      callToAction: "",
      buttonText: "",
      description: "",
      url: "",
      placement: "HERO",
      textAlignment: "CENTER",
      position: 0,
      width: "",
      height: "",
      backgroundColor: "",
      textColor: "",
      deviceTarget: "ALL",
      startDate: new Date(),
      isActive: true,
    }
  });

  const handleImageUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value;
    form.setValue("imageUrl", url);
    setPreviewUrl(url);
  };

  const onSubmit = async (data: CreateBannerFormValues | UpdateBannerFormValues) => {
    setIsSubmitting(true);
    try {
      if (isEditing && banner) {
        const updateData: UpdateBannerFormValues = {
          ...data,
          id: banner.id,
        };
        const result = await updateBanner(updateData);
        if (result.success) {
          toast.success("Banner updated successfully");
          router.push("/banner");
        } else {
          toast.error(result.error || "Failed to update banner");
        }
      } else {
        const result = await createBanner(data as CreateBannerFormValues);
        if (result.success) {
          toast.success("Banner created successfully");
          router.push("/banner");
        } else {
          toast.error(result.error || "Failed to create banner");
        }
      }
    } catch (error) {
      toast.error("An unexpected error occurred");
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
      <div className="md:col-span-2">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title*</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="subtitle"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Subtitle</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="imageUrl"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Image URL*</FormLabel>
                  <FormControl>
                    <Input 
                      {...field} 
                      onChange={handleImageUrlChange}
                    />
                  </FormControl>
                  <FormDescription>
                    URL to the banner image (recommended size: 1200x400px)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="mobileImageUrl"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Mobile Image URL</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormDescription>
                    Optional separate image for mobile devices (recommended size: 600x400px)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="callToAction"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Call to Action</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="buttonText"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Button Text</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>URL</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormDescription>
                    Where users will be directed when clicking the banner
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      {...field} 
                      rows={3}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="placement"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Placement*</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select placement" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {/* <SelectItem value="HOME">Home Page</SelectItem> */}
                        <SelectItem value="HERO">Hero Section</SelectItem>
                        <SelectItem value="CATEGORY_SECTION_LANDING_PAGE">Category Section Landing Page</SelectItem>
                        <SelectItem value="CATEGORY">Category Page</SelectItem>
                        <SelectItem value="PRODUCT">Product Page</SelectItem>
                        <SelectItem value="CHECKOUT">Checkout</SelectItem>
                        <SelectItem value="SIDEBAR">Sidebar</SelectItem>
                        <SelectItem value="HEADER">Header</SelectItem>
                        <SelectItem value="FOOTER">Footer</SelectItem>
                        <SelectItem value="POPUP">Popup</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

            <FormField
              control={form.control}
              name="textAlignment"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Text Alignment</FormLabel>
                  <Select 
                    onValueChange={field.onChange} 
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select text alignment" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {
                        //if placement = CATEGORY_SECTION_LANDING_PAGE, show bottom
                        form.watch("placement") === "CATEGORY_SECTION_LANDING_PAGE" ? (
                          <SelectItem value="BOTTOM">Bottom</SelectItem>
                        ) :                       
                        <><SelectItem value="LEFT">Left</SelectItem>
                          <SelectItem value="CENTER">Center</SelectItem>
                          <SelectItem value="RIGHT">Right</SelectItem>
                        </>
                      }
                    </SelectContent>
                    </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
              
              <FormField
                control={form.control}
                name="position"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Position*</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        min="0"
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      Display order (0 = highest priority)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="width"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Width</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="e.g., 100%, 500px" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="height"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Height</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="e.g., 300px, auto" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="backgroundColor"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Background Color</FormLabel>
                    <FormControl>
                      <div className="flex gap-2">
                        <Input {...field} placeholder="#ffffff" />
                        <Input 
                          type="color" 
                          value={field.value || "#ffffff"}
                          onChange={(e) => field.onChange(e.target.value)}
                          className="w-12 p-1 h-10"
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="textColor"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Text Color</FormLabel>
                    <FormControl>
                      <div className="flex gap-2">
                        <Input {...field} placeholder="#000000" />
                        <Input 
                          type="color" 
                          value={field.value || "#000000"}
                          onChange={(e) => field.onChange(e.target.value)}
                          className="w-12 p-1 h-10"
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="deviceTarget"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Device Target*</FormLabel>
                  <Select 
                    onValueChange={field.onChange} 
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select device target" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="ALL">All Devices</SelectItem>
                      <SelectItem value="DESKTOP">Desktop Only</SelectItem>
                      <SelectItem value="MOBILE">Mobile Only</SelectItem>
                      <SelectItem value="TABLET">Tablet Only</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Start Date*</FormLabel>
                    <FormControl>
                      <Input 
                        type="datetime-local" 
                        {...field}
                        value={field.value instanceof Date 
                          ? new Date(field.value.getTime() - field.value.getTimezoneOffset() * 60000).toISOString().slice(0, 16)
                          : ''}
                        onChange={(e) => field.onChange(new Date(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>End Date<FormDescription>leave empty for no end date</FormDescription></FormLabel>
                    <FormControl>
                      <Input 
                        type="datetime-local" 
                        {...field}
                        value={field.value instanceof Date 
                          ? new Date(field.value.getTime() - field.value.getTimezoneOffset() * 60000).toISOString().slice(0, 16)
                          : ''}
                        onChange={(e) => field.onChange(e.target.value ? new Date(e.target.value) : undefined)}
                      />
                    </FormControl>

                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="isActive"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Active Status</FormLabel>
                    <FormDescription>
                      Enable or disable this banner
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <div className="flex justify-end gap-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => router.push("/banner")}
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={isSubmitting}
              >
                {isSubmitting ? "Saving..." : isEditing ? "Update Banner" : "Create Banner"}
              </Button>
            </div>
          </form>
        </Form>
      </div>

      <div className="md:col-span-1">
        <Card>
          <CardContent className="p-4">
            <h3 className="text-lg font-medium mb-4">Banner Preview</h3>
            {previewUrl ? (
              <div className="border rounded-md overflow-hidden">
                <img 
                  src={previewUrl} 
                  alt="Banner preview" 
                  className="w-full h-auto"
                  onError={() => setPreviewUrl("")}
                />
              </div>
            ) : (
              <div className="border rounded-md p-8 text-center text-gray-500">
                Enter an image URL to see a preview
              </div>
            )}
            
            <div className="mt-6 space-y-4">
              <h4 className="font-medium">Banner Details</h4>
              <p className="text-sm text-gray-500">
                <strong>Title:</strong> {form.watch("title") || "Not set"}
              </p>
              {form.watch("subtitle") && (
                <p className="text-sm text-gray-500">
                  <strong>Subtitle:</strong> {form.watch("subtitle")}
                </p>
              )}
              <p className="text-sm text-gray-500">
                <strong>Placement:</strong> {form.watch("placement")}
              </p>
              <p className="text-sm text-gray-500">
                <strong>Status:</strong> {form.watch("isActive") ? "Active" : "Inactive"}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}




