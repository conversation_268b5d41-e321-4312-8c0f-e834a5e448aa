"use client";

import { useState, useTransition } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Search, X } from "lucide-react";

export default function UserFilters() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  
  // Get current filter values from URL
  const currentQuery = searchParams.get("query") || "";
  const currentRole = searchParams.get("role") || "all";
  const currentStatus = searchParams.get("status") || "all";
  
  // Local state for form inputs
  const [query, setQuery] = useState(currentQuery);
  const [role, setRole] = useState(currentRole);
  const [status, setStatus] = useState(currentStatus);
  
  // Apply filters
  const applyFilters = () => {
    startTransition(() => {
      // Create new URLSearchParams
      const params = new URLSearchParams(searchParams);
      
      // Update or remove query parameter
      if (query) {
        params.set("query", query);
      } else {
        params.delete("query");
      }
      
      // Update or remove role parameter
      if (role && role !== "all") {
        params.set("role", role);
      } else {
        params.delete("role");
      }
      
      // Update or remove status parameter
      if (status && status !== "all") {
        params.set("status", status);
      } else {
        params.delete("status");
      }
      
      // Reset to page 1 when filters change
      params.set("page", "1");
      
      // Update URL
      router.push(`/users?${params.toString()}`);
    });
  };
  
  // Clear all filters
  const clearFilters = () => {
    setQuery("");
    setRole("");
    setStatus("");
    
    startTransition(() => {
      // Keep only pagination and sorting parameters
      const params = new URLSearchParams();
      const page = searchParams.get("page");
      const perPage = searchParams.get("perPage");
      const sort = searchParams.get("sort");
      const order = searchParams.get("order");
      
      if (page) params.set("page", page);
      if (perPage) params.set("perPage", perPage);
      if (sort) params.set("sort", sort);
      if (order) params.set("order", order);
      
      router.push(`/users?${params.toString()}`);
    });
  };
  
  return (
    <div className="bg-white p-4 rounded-lg shadow mb-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="relative">
          <Input
            placeholder="Search users..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="pl-9"
          />
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        </div>
        
        <Select value={role} onValueChange={setRole}>
          <SelectTrigger>
            <SelectValue placeholder="Filter by role" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Roles</SelectItem>
            <SelectItem value="administAB">Administrator</SelectItem>
            <SelectItem value="moderatorAB">Moderator</SelectItem>
            <SelectItem value="inregistratAB">Registered</SelectItem>
            <SelectItem value="angajatAB">Employee</SelectItem>
            <SelectItem value="fourLvlAdminAB">Level 4 Admin</SelectItem>
            <SelectItem value="fourLvlInregistratAB">Level 4 Registered</SelectItem>
          </SelectContent>
        </Select>
        
        <Select value={status} onValueChange={setStatus}>
          <SelectTrigger>
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
            <SelectItem value="suspended">Suspended</SelectItem>
            <SelectItem value="deleted">Deleted</SelectItem>
          </SelectContent>
        </Select>
        
        <div className="flex gap-2">
          <Button 
            onClick={applyFilters} 
            disabled={isPending}
            className="flex-1"
          >
            Apply Filters
          </Button>
          <Button 
            variant="outline" 
            onClick={clearFilters}
            disabled={isPending}
            className="px-3"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}



