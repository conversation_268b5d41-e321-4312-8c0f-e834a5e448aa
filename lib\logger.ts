// logger.ts
import { createLogger, format, transports, Logger as <PERSON><PERSON><PERSON><PERSON> } from "winston"; // Import Logger type
import DailyRotateFile from 'winston-daily-rotate-file';

// Define a type for objects that might have a 'message' property
interface HasMessage {
  message: unknown; // Use unknown here, as the message itself could be anything
}

// Define a type for objects that might have a 'stack' property (like Error objects)
interface HasStack {
  stack?: string;
}

// Type guard to check if an unknown value has a 'message' property that is a string
function isHasStringMessage(error: unknown): error is HasMessage {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof (error as HasMessage).message === 'string'
  );
}

// Type guard to check if an unknown value has a 'stack' property that is a string
function isHasStack(error: unknown): error is HasStack {
  return (
    typeof error === 'object' &&
    error !== null &&
    'stack' in error &&
    typeof (error as HasStack).stack === 'string'
  );
}

const transport1: DailyRotateFile = new DailyRotateFile({
        filename: 'logs/error-%DATE%.log',
        datePattern: 'YYYY-MM-DD',
        zippedArchive: true,
        maxSize: '20m',
        maxFiles: '140000d', // Keep logs for 140000 days
        level: 'error',
});

// transport1.on('error', error => {
//     // log or handle errors here
// });


// transport1.on('rotate', (oldFilename, newFilename) => {
//     // do something fun
// });

const transport2: DailyRotateFile = new DailyRotateFile({
        filename: 'logs/combined-%DATE%.log',
        datePattern: 'YYYY-MM-DD',
        zippedArchive: true,
        maxSize: '20m',
        maxFiles: '140000d',
});

// transport2.on('error', error => {
//     // log or handle errors here 
//     //send to Sentry or Grafana
// });


// transport2.on('rotate', (oldFilename, newFilename) => {
//     // do something fun
// });

// Configure the Winston logger
const logger: WinstonLogger = createLogger({ // Type the logger instance
  level: process.env.NODE_ENV === "production" ? "info" : "debug", // Log level based on environment
  format: format.combine(
    format.timestamp({ format: "YYYY-MM-DD HH:mm:ss" }), // Optional timestamp field
    format.errors({ stack: true }), // Crucial for including stack traces in JSON
    format.json() // Output as JSON
  ),
  transports: [
    new transports.Console(), // Log to console
    transport1,
    transport2
  ],

});

/**
 * Logs an error message, including error details if provided.
 * Handles standard Error objects, objects with a 'message' property,
 * and other unknown types gracefully.
 * @param message The primary error message.
 * @param error The error object (e.g., Error, or any object with a message/stack).
 */
export const logError = (message: string, error?: unknown) => {
  let errorDetails = ''; // Use a separate variable for error details

  if (error instanceof Error) {
    errorDetails = `: ${error.message}`;
    if (error.stack) {
      errorDetails += `\nStack: ${error.stack}`;
    }
  } else if (isHasStringMessage(error)) {
    // Now TypeScript knows error.message is a string here
    errorDetails = `: ${error.message}`;
    if (isHasStack(error)) { // Check for stack even on non-Error objects
      errorDetails += `\nStack: ${error.stack}`;
    }
  } else if (error) {
    // Fallback for anything else that's not null/undefined
    errorDetails = `: ${String(error)}`;
  }

  // Combine the main message with the extracted error details
  logger.error(`${message}${errorDetails}`);
};

// Your existing logInfo and logDebug functions
export const logInfo = (message: string) => {
  logger.info(message);
};

export const logDebug = (message: string) => {
  if (process.env.NODE_ENV !== "production") {
    logger.debug(message);
  }
};

export default logger;