{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-04 13:53:01"}
{"level":"info","message":"getProductAttributes -> Product \"11531743192\" has the attributes: curea -- piele,cadran -- <NAME_EMAIL>","timestamp":"2025-08-04 13:53:40"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11531743192\" with <NAME_EMAIL>","timestamp":"2025-08-04 13:53:40"}
{"level":"info","message":"getProductAttributes -> Product \"07119963155\" has the attributes: marime -- <NAME_EMAIL>","timestamp":"2025-08-04 13:56:23"}
{"level":"info","message":"getProductAttributesHistory -> Product \"07119963155\" with <NAME_EMAIL>","timestamp":"2025-08-04 13:56:23"}
{"level":"info","message":"ProductAddAttributeAction -> Attribute \"culoare\" added successfully for product 07119963155 by <EMAIL>","timestamp":"2025-08-04 13:56:30"}
{"level":"info","message":"getProductAttributes -> Product \"07119963155\" has the attributes: marime -- XL,culoare -- <NAME_EMAIL>","timestamp":"2025-08-04 13:56:30"}
{"level":"info","message":"getProductAttributesHistory -> Product \"07119963155\" with <NAME_EMAIL>","timestamp":"2025-08-04 13:56:30"}
{"level":"info","message":"User status changed for user cmdwxot5n000shx0of0ymzh6z with changes: {\"isActive\":true,\"isSuspended\":true,\"suspensionReason\":\"Administrative action\"} by <EMAIL>","timestamp":"2025-08-04 14:02:53"}
{"level":"error","message":"Error fetching user with ID cmdwxot5n000shx0of0ymzh6z:: [object Object]","timestamp":"2025-08-04 15:26:50"}
{"level":"error","message":"Error fetching user with ID cmdwxot5n000shx0of0ymzh6z:: [object Object]","timestamp":"2025-08-04 15:26:59"}
{"level":"error","message":"Error fetching user with ID cmdwxot5n000shx0of0ymzh6z:: [object Object]","timestamp":"2025-08-04 15:27:07"}
{"level":"info","message":"User status changed for user cmcofujti0002hxpw1euu9580 with changes: {\"isActive\":false,\"isSuspended\":false,\"suspensionReason\":\"\"} by <EMAIL>","timestamp":"2025-08-04 15:27:37"}
{"level":"info","message":"User status changed for user cmcofujti0002hxpw1euu9580 with changes: {\"isActive\":true,\"isSuspended\":false,\"suspensionReason\":\"\"} by <EMAIL>","timestamp":"2025-08-04 15:28:45"}
{"level":"info","message":"User status changed for user cmcofujti0002hxpw1euu9580 with changes: {\"isActive\":true,\"isSuspended\":true,\"suspensionReason\":\"Administrative action\"} by <EMAIL>","timestamp":"2025-08-04 15:29:09"}
{"level":"info","message":"User status changed for user cmcofujti0002hxpw1euu9580 with changes: {\"isActive\":true,\"isSuspended\":false,\"suspensionReason\":\"\"} by <EMAIL>","timestamp":"2025-08-04 15:32:11"}
{"level":"info","message":"User cmdwxot5n000shx0of0ymzh6z details retrieved for userEmail","timestamp":"2025-08-04 15:48:17"}
{"level":"info","message":"User updated successfully: cosmin.oprea@automobilebavaria.<NAME_EMAIL> with changes: {\"role\":{\"from\":\"administAB\",\"to\":\"moderatorAB\"}}","timestamp":"2025-08-04 15:49:05"}
{"level":"info","message":"User cmdwxot5n000shx0of0ymzh6z details retrieved for userEmail","timestamp":"2025-08-04 15:49:05"}
{"level":"info","message":"User cmdwxot5n000shx0of0ymzh6z details retrieved for userEmail","timestamp":"2025-08-04 15:49:05"}
{"level":"info","message":"User cmdwxot5n000shx0of0ymzh6z details retrieved for userEmail","timestamp":"2025-08-04 15:49:12"}
{"level":"info","message":"User cmdwxot5n000shx0of0ymzh6z details retrieved for userEmail","timestamp":"2025-08-04 15:50:03"}
{"level":"info","message":"User cmdwxot5n000shx0of0ymzh6z details retrieved for userEmail","timestamp":"2025-08-04 15:50:13"}
{"level":"info","message":"User cmdwxot5n000shx0of0ymzh6z details retrieved for userEmail","timestamp":"2025-08-04 15:53:11"}
{"level":"info","message":"User cmdwxot5n000shx0of0ymzh6z details retrieved for userEmail","timestamp":"2025-08-04 15:53:12"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-04 15:53:45"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-04 15:55:24"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-04 15:55:34"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-04 15:56:40"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-04 15:56:45"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-04 15:59:21"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-04 16:00:12"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-04 16:01:05"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-04 16:01:11"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-04 16:01:20"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-04 16:07:30"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-04 16:30:20"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-04 16:30:35"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-04 16:30:36"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-04 16:30:41"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-04 16:30:42"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-04 16:31:21"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-04 16:35:43"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-04 16:36:32"}
{"level":"info","message":"Group created: cmdx5t8vt000ghx8c7srgv4<NAME_EMAIL>","timestamp":"2025-08-04 16:42:06"}
{"level":"info","message":"Group updated: cmdx5t8vt000ghx8c7srgv4<NAME_EMAIL>","timestamp":"2025-08-04 16:56:33"}
