"use server";

import { revalidatePath } from "next/cache";
import prisma from "@/app/utils/db";
import { logError, logInfo } from "@/lib/logger";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { ReturnAction } from "./actions";
import { z } from "zod";

const groupSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name too long"),
  description: z.string().max(500, "Description too long").optional(),
  permissions: z.array(z.string()).min(1, "At least one permission is required"),
});

type GroupFormValues = z.infer<typeof groupSchema>;

export async function createGroup(data: GroupFormValues): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email;

  try {
    const validatedData = groupSchema.parse(data);

    // Check if group name already exists
    const existingGroup = await prisma.userGroup.findUnique({
      where: { name: validatedData.name }
    });

    if (existingGroup) {
      return {
        status: "ERROR",
        message: "A group with this name already exists",
      };
    }

    const group = await prisma.userGroup.create({
      data: {
        name: validatedData.name,
        description: validatedData.description,
        permissions: validatedData.permissions,
        createdBy: actorEmail,
        updatedBy: actorEmail,
      },
    });

    revalidatePath("/groups");
    logInfo(`Group created: ${group.id} by ${actorEmail}`);

    return {
      status: "SUCCESS",
      message: "Group created successfully",
    };
  } catch (error) {
    logError(`Error creating group by ${actorEmail}:`, error);
    return {
      status: "ERROR",
      message: "Failed to create group",
    };
  }
}

export async function updateGroup(id: string, data: GroupFormValues): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email;

  try {
    const validatedData = groupSchema.parse(data);

    // Check if another group with this name exists
    const existingGroup = await prisma.userGroup.findFirst({
      where: { 
        name: validatedData.name,
        NOT: { id }
      }
    });

    if (existingGroup) {
      return {
        status: "ERROR",
        message: "A group with this name already exists",
      };
    }

    await prisma.userGroup.update({
      where: { id },
      data: {
        name: validatedData.name,
        description: validatedData.description,
        permissions: validatedData.permissions,
        updatedBy: actorEmail,
      },
    });

    revalidatePath("/groups");
    revalidatePath(`/groups/${id}`);
    logInfo(`Group updated: ${id} by ${actorEmail}`);

    return {
      status: "SUCCESS",
      message: "Group updated successfully",
    };
  } catch (error) {
    logError(`Error updating group ${id} by ${actorEmail}:`, error);
    return {
      status: "ERROR",
      message: "Failed to update group",
    };
  }
}

export async function deleteGroup(id: string): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email;

  try {
    // Check if group has users
    const group = await prisma.userGroup.findUnique({
      where: { id },
      include: { _count: { select: { users: true } } }
    });

    if (!group) {
      return {
        status: "ERROR",
        message: "Group not found",
      };
    }

    if (group._count.users > 0) {
      return {
        status: "ERROR",
        message: "Cannot delete group with active users. Remove all users first.",
      };
    }

    await prisma.userGroup.delete({
      where: { id },
    });

    revalidatePath("/groups");
    logInfo(`Group deleted: ${id} by ${actorEmail}`);

    return {
      status: "SUCCESS",
      message: "Group deleted successfully",
    };
  } catch (error) {
    logError(`Error deleting group ${id} by ${actorEmail}:`, error);
    return {
      status: "ERROR",
      message: "Failed to delete group",
    };
  }
}