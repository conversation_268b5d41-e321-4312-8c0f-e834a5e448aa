{"level":"error","message":"DiscountUpdateAction -> Error at parsing: [\n  {\n    \"code\": \"custom\",\n    \"message\": \"End date cannot be in the past\",\n    \"path\": [\n      \"endDate\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-06-13 10:28:00"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-13 14:28:04"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-13 14:32:16"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-13 14:32:34"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-13 14:35:09"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-13 14:50:13"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-13 14:50:14"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-13 14:50:15"}
