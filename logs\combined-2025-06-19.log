{"level":"info","message":"Starting updateProductPrice for product cmc2a4p3t05lthkhxb3lagi70","timestamp":"2025-06-19 10:03:34"}
{"level":"info","message":"Found product 12317501755 with base price 100","timestamp":"2025-06-19 10:03:34"}
{"level":"info","message":"No active discount found for product 12317501755","timestamp":"2025-06-19 10:03:34"}
{"level":"info","message":"Price changed for 12317501755: 75 -> 100","timestamp":"2025-06-19 10:03:34"}
{"level":"info","message":"Updated product 12317501755 with new price 100","timestamp":"2025-06-19 10:03:34"}
{"level":"info","message":"Created price history record for 12317501755","timestamp":"2025-06-19 10:03:34"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmc2a4p3t05lthkhxb3lagi70 for the discountId cmbuqdkl00003hxv4vm6utec5 by <EMAIL>","timestamp":"2025-06-19 10:03:34"}
{"level":"info","message":"Discount with name cu fix created <NAME_EMAIL>","timestamp":"2025-06-19 10:04:02"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmc2a4p3t05lthkhxb3lagi70 (12317501755)","timestamp":"2025-06-19 10:04:09"}
{"level":"info","message":"Starting updateProductPrice for product cmc2a4p3t05lthkhxb3lagi70","timestamp":"2025-06-19 10:04:09"}
{"level":"info","message":"Found product 12317501755 with base price 100","timestamp":"2025-06-19 10:04:09"}
{"level":"info","message":"Found active discount for 12317501755: FIXED_AMOUNT with value 5","timestamp":"2025-06-19 10:04:09"}
{"level":"info","message":"FIXED_AMOUNT: 100 - 5 = 95","timestamp":"2025-06-19 10:04:09"}
{"level":"info","message":"Discount applied: 12317501755 - Original: 100, Final: 95, Discount %: 5","timestamp":"2025-06-19 10:04:09"}
{"level":"info","message":"Price changed for 12317501755: 100 -> 95","timestamp":"2025-06-19 10:04:09"}
{"level":"info","message":"Updated product 12317501755 with new price 95","timestamp":"2025-06-19 10:04:09"}
{"level":"info","message":"Created price history record for 12317501755","timestamp":"2025-06-19 10:04:09"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-19 10:04:09"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmc31c4xk0004hxm0ivirsti1 updated <NAME_EMAIL>","timestamp":"2025-06-19 10:04:09"}
{"level":"info","message":"Starting updateProductPrice for product cmc2a4fv00006hkhx9kbr5lh6","timestamp":"2025-06-19 10:04:20"}
{"level":"info","message":"Found product 01200426532 with base price 200","timestamp":"2025-06-19 10:04:20"}
{"level":"info","message":"No active discount found for product 01200426532","timestamp":"2025-06-19 10:04:20"}
{"level":"info","message":"Price changed for 01200426532: 150 -> 200","timestamp":"2025-06-19 10:04:20"}
{"level":"info","message":"Updated product 01200426532 with new price 200","timestamp":"2025-06-19 10:04:20"}
{"level":"info","message":"Created price history record for 01200426532","timestamp":"2025-06-19 10:04:20"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmc2a4fv00006hkhx9kbr5lh6 for the discountId cmbuqdkl00003hxv4vm6utec5 by <EMAIL>","timestamp":"2025-06-19 10:04:20"}
{"level":"info","message":"Discount with name pret fix created <NAME_EMAIL>","timestamp":"2025-06-19 10:04:49"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmc2a4fv00006hkhx9kbr5lh6 (01200426532)","timestamp":"2025-06-19 10:04:54"}
{"level":"info","message":"Starting updateProductPrice for product cmc2a4fv00006hkhx9kbr5lh6","timestamp":"2025-06-19 10:04:54"}
{"level":"info","message":"Found product 01200426532 with base price 200","timestamp":"2025-06-19 10:04:54"}
{"level":"info","message":"Found active discount for 01200426532: NEW_PRICE with value 11","timestamp":"2025-06-19 10:04:54"}
{"level":"info","message":"NEW_PRICE: New price set to 11","timestamp":"2025-06-19 10:04:54"}
{"level":"info","message":"Discount applied: 01200426532 - Original: 200, Final: 11, Discount %: 94.5","timestamp":"2025-06-19 10:04:54"}
{"level":"info","message":"Price changed for 01200426532: 200 -> 11","timestamp":"2025-06-19 10:04:54"}
{"level":"info","message":"Updated product 01200426532 with new price 11","timestamp":"2025-06-19 10:04:54"}
{"level":"info","message":"Created price history record for 01200426532","timestamp":"2025-06-19 10:04:54"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-19 10:04:54"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmc31d58b000hhxm08e6ii38s updated <NAME_EMAIL>","timestamp":"2025-06-19 10:04:54"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmc2a4g13006thkhxd1x1anc0 (07119963155)","timestamp":"2025-06-19 10:07:07"}
{"level":"info","message":"Starting updateProductPrice for product cmc2a4g13006thkhxd1x1anc0","timestamp":"2025-06-19 10:07:07"}
{"level":"info","message":"Found product 07119963155 with base price 12","timestamp":"2025-06-19 10:07:07"}
{"level":"info","message":"Found active discount for 07119963155: NEW_PRICE with value 11","timestamp":"2025-06-19 10:07:07"}
{"level":"info","message":"NEW_PRICE: New price set to 11","timestamp":"2025-06-19 10:07:07"}
{"level":"info","message":"Discount applied: 07119963155 - Original: 12, Final: 11, Discount %: 8.3333333333333333333","timestamp":"2025-06-19 10:07:07"}
{"level":"info","message":"Price changed for 07119963155: 12 -> 11","timestamp":"2025-06-19 10:07:07"}
{"level":"info","message":"Updated product 07119963155 with new price 11","timestamp":"2025-06-19 10:07:07"}
{"level":"info","message":"Created price history record for 07119963155","timestamp":"2025-06-19 10:07:07"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-19 10:07:07"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmc31d58b000hhxm08e6ii38s updated <NAME_EMAIL>","timestamp":"2025-06-19 10:07:07"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmc2a4g5p00ckhkhx0e5h2bee (11122246093)","timestamp":"2025-06-19 10:08:43"}
{"level":"info","message":"Starting updateProductPrice for product cmc2a4g5p00ckhkhx0e5h2bee","timestamp":"2025-06-19 10:08:43"}
{"level":"info","message":"Found product 11122246093 with base price 54","timestamp":"2025-06-19 10:08:43"}
{"level":"info","message":"Found active discount for 11122246093: NEW_PRICE with value 11","timestamp":"2025-06-19 10:08:43"}
{"level":"info","message":"NEW_PRICE: New price set to 11","timestamp":"2025-06-19 10:08:43"}
{"level":"info","message":"Discount applied: 11122246093 - Original: 54, Final: 11, Discount %: 79.62962962962962963","timestamp":"2025-06-19 10:08:43"}
{"level":"info","message":"Price changed for 11122246093: 54 -> 11","timestamp":"2025-06-19 10:08:43"}
{"level":"info","message":"Updated product 11122246093 with new price 11","timestamp":"2025-06-19 10:08:43"}
{"level":"info","message":"Created price history record for 11122246093","timestamp":"2025-06-19 10:08:43"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-19 10:08:43"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmc31d58b000hhxm08e6ii38s updated <NAME_EMAIL>","timestamp":"2025-06-19 10:08:43"}
