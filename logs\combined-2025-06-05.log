{"level":"error","message":"DiscountAddProductAction -> Product 11422247017 already has an active discount by cosmin.oprea","timestamp":"2025-06-05 08:13:01"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx46m05ljg4hx5eljbp99 updated with success with final price 21 and hasDiscount false","timestamp":"2025-06-05 08:16:03"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxrq0r0003hxnggwgyaasr updated successfully by cosmin.oprea","timestamp":"2025-06-05 08:16:03"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx41005k6g4hx1jn22z7u updated with success with final price 21 and hasDiscount false","timestamp":"2025-06-05 08:18:33"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxsh5h0006hxngai17nk9l updated successfully by cosmin.oprea","timestamp":"2025-06-05 08:18:33"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx41g05kbg4hxh1q012c1 updated with success with final price 21 and hasDiscount false","timestamp":"2025-06-05 08:21:19"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41g05kbg4hxh1q012c1 for the discountId cmbhxsh5h0006hxngai17nk9l by cosmin.oprea","timestamp":"2025-06-05 08:21:19"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx41k05kcg4hxhce2bg9q updated with success with final price 21 and hasDiscount false","timestamp":"2025-06-05 08:21:21"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41k05kcg4hxhce2bg9q for the discountId cmbhxsh5h0006hxngai17nk9l by cosmin.oprea","timestamp":"2025-06-05 08:21:21"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx41005k6g4hx1jn22z7u updated with success with final price 21 and hasDiscount false","timestamp":"2025-06-05 08:21:23"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41005k6g4hx1jn22z7u for the discountId cmbhxsh5h0006hxngai17nk9l by cosmin.oprea","timestamp":"2025-06-05 08:21:23"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx41205k7g4hx3n0uc1xw updated with success with final price 21 and hasDiscount false","timestamp":"2025-06-05 08:21:28"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41205k7g4hx3n0uc1xw for the discountId cmbhxrq0r0003hxnggwgyaasr by cosmin.oprea","timestamp":"2025-06-05 08:21:28"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx46m05ljg4hx5eljbp99 updated with success with final price 21 and hasDiscount false","timestamp":"2025-06-05 08:21:30"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx46m05ljg4hx5eljbp99 for the discountId cmbhxrq0r0003hxnggwgyaasr by cosmin.oprea","timestamp":"2025-06-05 08:21:30"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx40n05k3g4hxcog64yp0 updated with success with final price 21 and hasDiscount false","timestamp":"2025-06-05 08:21:34"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx40n05k3g4hxcog64yp0 for the discountId cmbhxr0en0000hxngzzwgceol by cosmin.oprea","timestamp":"2025-06-05 08:21:34"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx40k05k2g4hxbwryaw0m updated with success with final price 150 and hasDiscount false","timestamp":"2025-06-05 08:21:36"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx40k05k2g4hxbwryaw0m for the discountId cmbhxr0en0000hxngzzwgceol by cosmin.oprea","timestamp":"2025-06-05 08:21:36"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx40k05k2g4hxbwryaw0m updated with success with final price 99.99 and hasDiscount true","timestamp":"2025-06-05 08:52:32"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxsh5h0006hxngai17nk9l updated successfully by cosmin.oprea","timestamp":"2025-06-05 08:52:32"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx40n05k3g4hxcog64yp0 updated with success with final price 21 and hasDiscount false","timestamp":"2025-06-05 08:53:04"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxsh5h0006hxngai17nk9l updated successfully by cosmin.oprea","timestamp":"2025-06-05 08:53:04"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx40n05k3g4hxcog64yp0 updated with success with final price 21 and hasDiscount false","timestamp":"2025-06-05 08:53:30"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx40n05k3g4hxcog64yp0 for the discountId cmbhxsh5h0006hxngai17nk9l by cosmin.oprea","timestamp":"2025-06-05 08:53:30"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx4ib05org4hx9yzwc1dk updated with success with final price 21 and hasDiscount false","timestamp":"2025-06-05 08:53:41"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxsh5h0006hxngai17nk9l updated successfully by cosmin.oprea","timestamp":"2025-06-05 08:53:41"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx41905k9g4hxebqu78vr updated with success with final price 99.99 and hasDiscount true","timestamp":"2025-06-05 09:02:56"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxsh5h0006hxngai17nk9l updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:02:56"}
{"level":"error","message":"DiscountAddProductAction -> Product 11531743192 already has an active discount by cosmin.oprea","timestamp":"2025-06-05 09:04:15"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx41d05kag4hx887kdsm4 updated with success with final price 121 and hasDiscount false","timestamp":"2025-06-05 09:04:29"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxrq0r0003hxnggwgyaasr updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:04:29"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx42605kig4hxbyaf5v8a updated with success with final price 21 and hasDiscount false","timestamp":"2025-06-05 09:05:01"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxrq0r0003hxnggwgyaasr updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:05:01"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx48405lvg4hxabjf4igx updated with success with final price 46.75 and hasDiscount true","timestamp":"2025-06-05 09:05:43"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxr0en0000hxngzzwgceol updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:05:43"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx48905lwg4hx3lm5ga41 updated with success with final price 56.95 and hasDiscount true","timestamp":"2025-06-05 09:05:49"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxr0en0000hxngzzwgceol updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:05:49"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx48d05lxg4hxe1ztbko8 updated with success with final price 17.85 and hasDiscount true","timestamp":"2025-06-05 09:05:54"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxr0en0000hxngzzwgceol updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:05:54"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx48405lvg4hxabjf4igx updated with success with final price 55 and hasDiscount false","timestamp":"2025-06-05 09:06:03"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx48405lvg4hxabjf4igx for the discountId cmbhxr0en0000hxngzzwgceol by cosmin.oprea","timestamp":"2025-06-05 09:06:03"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx48905lwg4hx3lm5ga41 updated with success with final price 67 and hasDiscount false","timestamp":"2025-06-05 09:06:05"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx48905lwg4hx3lm5ga41 for the discountId cmbhxr0en0000hxngzzwgceol by cosmin.oprea","timestamp":"2025-06-05 09:06:05"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx48d05lxg4hxe1ztbko8 updated with success with final price 21 and hasDiscount false","timestamp":"2025-06-05 09:06:06"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx48d05lxg4hxe1ztbko8 for the discountId cmbhxr0en0000hxngzzwgceol by cosmin.oprea","timestamp":"2025-06-05 09:06:06"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx41d05kag4hx887kdsm4 updated with success with final price 121 and hasDiscount false","timestamp":"2025-06-05 09:07:24"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41d05kag4hx887kdsm4 for the discountId cmbhxrq0r0003hxnggwgyaasr by cosmin.oprea","timestamp":"2025-06-05 09:07:24"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx42605kig4hxbyaf5v8a updated with success with final price 21 and hasDiscount false","timestamp":"2025-06-05 09:07:25"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx42605kig4hxbyaf5v8a for the discountId cmbhxrq0r0003hxnggwgyaasr by cosmin.oprea","timestamp":"2025-06-05 09:07:25"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx40k05k2g4hxbwryaw0m updated with success with final price 180 and hasDiscount false","timestamp":"2025-06-05 09:07:30"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx40k05k2g4hxbwryaw0m for the discountId cmbhxsh5h0006hxngai17nk9l by cosmin.oprea","timestamp":"2025-06-05 09:07:30"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx4ib05org4hx9yzwc1dk updated with success with final price 21 and hasDiscount false","timestamp":"2025-06-05 09:07:31"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx4ib05org4hx9yzwc1dk for the discountId cmbhxsh5h0006hxngai17nk9l by cosmin.oprea","timestamp":"2025-06-05 09:07:31"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx41905k9g4hxebqu78vr updated with success with final price 211 and hasDiscount false","timestamp":"2025-06-05 09:07:32"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41905k9g4hxebqu78vr for the discountId cmbhxsh5h0006hxngai17nk9l by cosmin.oprea","timestamp":"2025-06-05 09:07:32"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxr0en0000hxngzzwgceol updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:08:57"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwwtwc0006g4hxdqu89rzi for the discountId cmbhxr0en0000hxngzzwgceol by cosmin.oprea","timestamp":"2025-06-05 09:09:00"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx41905k9g4hxebqu78vr updated with success with final price 179.35 and hasDiscount true","timestamp":"2025-06-05 09:09:16"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxr0en0000hxngzzwgceol updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:09:16"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx41905k9g4hxebqu78vr updated with success with final price 211 and hasDiscount false","timestamp":"2025-06-05 09:09:27"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41905k9g4hxebqu78vr for the discountId cmbhxr0en0000hxngzzwgceol by cosmin.oprea","timestamp":"2025-06-05 09:09:27"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx41905k9g4hxebqu78vr updated with success with final price 211 and hasDiscount false","timestamp":"2025-06-05 09:09:40"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxrq0r0003hxnggwgyaasr updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:09:40"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx48405lvg4hxabjf4igx updated with success with final price 55 and hasDiscount false","timestamp":"2025-06-05 09:09:53"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxrq0r0003hxnggwgyaasr updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:09:53"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx41905k9g4hxebqu78vr updated with success with final price 211 and hasDiscount false","timestamp":"2025-06-05 09:09:58"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41905k9g4hxebqu78vr for the discountId cmbhxrq0r0003hxnggwgyaasr by cosmin.oprea","timestamp":"2025-06-05 09:09:58"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx48405lvg4hxabjf4igx updated with success with final price 55 and hasDiscount false","timestamp":"2025-06-05 09:10:00"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx48405lvg4hxabjf4igx for the discountId cmbhxrq0r0003hxnggwgyaasr by cosmin.oprea","timestamp":"2025-06-05 09:10:00"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx41905k9g4hxebqu78vr updated with success with final price 99.99 and hasDiscount true","timestamp":"2025-06-05 09:10:13"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxsh5h0006hxngai17nk9l updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:10:13"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx48405lvg4hxabjf4igx updated with success with final price 55 and hasDiscount false","timestamp":"2025-06-05 09:10:16"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxsh5h0006hxngai17nk9l updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:10:16"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx48405lvg4hxabjf4igx updated with success with final price 55 and hasDiscount false","timestamp":"2025-06-05 09:10:36"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx48405lvg4hxabjf4igx for the discountId cmbhxsh5h0006hxngai17nk9l by cosmin.oprea","timestamp":"2025-06-05 09:10:36"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx41905k9g4hxebqu78vr updated with success with final price 211 and hasDiscount false","timestamp":"2025-06-05 09:10:38"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41905k9g4hxebqu78vr for the discountId cmbhxsh5h0006hxngai17nk9l by cosmin.oprea","timestamp":"2025-06-05 09:10:38"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx41905k9g4hxebqu78vr updated with success with final price 99.99 and hasDiscount true","timestamp":"2025-06-05 09:13:08"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxsh5h0006hxngai17nk9l updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:13:08"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx41905k9g4hxebqu78vr updated with success with final price 211 and hasDiscount false","timestamp":"2025-06-05 09:13:13"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41905k9g4hxebqu78vr for the discountId cmbhxsh5h0006hxngai17nk9l by cosmin.oprea","timestamp":"2025-06-05 09:13:13"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx41905k9g4hxebqu78vr updated with success with final price 179.35 and hasDiscount true","timestamp":"2025-06-05 09:13:18"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxr0en0000hxngzzwgceol updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:13:18"}
{"level":"info","message":"updateProductPrice-> for product cmbhwx41905k9g4hxebqu78vr updated with success with final price 211 and hasDiscount false","timestamp":"2025-06-05 09:13:21"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41905k9g4hxebqu78vr for the discountId cmbhxr0en0000hxngzzwgceol by cosmin.oprea","timestamp":"2025-06-05 09:13:21"}
{"level":"info","message":"Processing discount for product 11531743192: NEW_PRICE discount with value 99.99","timestamp":"2025-06-05 09:19:07"}
{"level":"info","message":"Discount applied: 11531743192 - Original: 211, Final: 99.99, Discount %: 52.611374407582938389","timestamp":"2025-06-05 09:19:07"}
{"level":"info","message":"Price changed for 11531743192: 211 -> 99.99","timestamp":"2025-06-05 09:19:07"}
{"level":"info","message":"Created price history record for 11531743192","timestamp":"2025-06-05 09:19:07"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxsh5h0006hxngai17nk9l updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:19:07"}
{"level":"info","message":"Processing discount for product 12317501755: NEW_PRICE discount with value 99.99","timestamp":"2025-06-05 09:19:21"}
{"level":"info","message":"Discount not applied: 12317501755 - Final price would not be lower","timestamp":"2025-06-05 09:19:21"}
{"level":"info","message":"No price change for 12317501755","timestamp":"2025-06-05 09:19:21"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxsh5h0006hxngai17nk9l updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:19:21"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-05 09:19:27"}
{"level":"info","message":"Price changed for 11531743192: 99.99 -> 211","timestamp":"2025-06-05 09:19:27"}
{"level":"info","message":"Created price history record for 11531743192","timestamp":"2025-06-05 09:19:27"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41905k9g4hxebqu78vr for the discountId cmbhxsh5h0006hxngai17nk9l by cosmin.oprea","timestamp":"2025-06-05 09:19:27"}
{"level":"info","message":"No active discount found for product 12317501755","timestamp":"2025-06-05 09:19:29"}
{"level":"info","message":"No price change for 12317501755","timestamp":"2025-06-05 09:19:29"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx48405lvg4hxabjf4igx for the discountId cmbhxsh5h0006hxngai17nk9l by cosmin.oprea","timestamp":"2025-06-05 09:19:29"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-05 09:19:45"}
{"level":"info","message":"No price change for 11531743192","timestamp":"2025-06-05 09:19:45"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxrq0r0003hxnggwgyaasr updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:19:45"}
{"level":"info","message":"No active discount found for product 12317501755","timestamp":"2025-06-05 09:19:56"}
{"level":"info","message":"No price change for 12317501755","timestamp":"2025-06-05 09:19:56"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxrq0r0003hxnggwgyaasr updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:19:56"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-05 09:20:11"}
{"level":"info","message":"No price change for 11531743192","timestamp":"2025-06-05 09:20:11"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41905k9g4hxebqu78vr for the discountId cmbhxrq0r0003hxnggwgyaasr by cosmin.oprea","timestamp":"2025-06-05 09:20:11"}
{"level":"info","message":"No active discount found for product 12317501755","timestamp":"2025-06-05 09:20:13"}
{"level":"info","message":"No price change for 12317501755","timestamp":"2025-06-05 09:20:13"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx48405lvg4hxabjf4igx for the discountId cmbhxrq0r0003hxnggwgyaasr by cosmin.oprea","timestamp":"2025-06-05 09:20:13"}
{"level":"info","message":"Processing discount for product 11531743192: PERCENTAGE discount with value 15","timestamp":"2025-06-05 09:20:19"}
{"level":"info","message":"Discount applied: 11531743192 - Original: 211, Final: 179.35, Discount %: 15","timestamp":"2025-06-05 09:20:19"}
{"level":"info","message":"Price changed for 11531743192: 211 -> 179.35","timestamp":"2025-06-05 09:20:19"}
{"level":"info","message":"Created price history record for 11531743192","timestamp":"2025-06-05 09:20:19"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxr0en0000hxngzzwgceol updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:20:19"}
{"level":"info","message":"Processing discount for product 12317501755: PERCENTAGE discount with value 15","timestamp":"2025-06-05 09:20:24"}
{"level":"info","message":"Discount applied: 12317501755 - Original: 55, Final: 46.75, Discount %: 15","timestamp":"2025-06-05 09:20:24"}
{"level":"info","message":"Price changed for 12317501755: 55 -> 46.75","timestamp":"2025-06-05 09:20:24"}
{"level":"info","message":"Created price history record for 12317501755","timestamp":"2025-06-05 09:20:24"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxr0en0000hxngzzwgceol updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:20:24"}
{"level":"info","message":"No active discount found for product 12317501755","timestamp":"2025-06-05 09:20:27"}
{"level":"info","message":"Price changed for 12317501755: 46.75 -> 55","timestamp":"2025-06-05 09:20:27"}
{"level":"info","message":"Created price history record for 12317501755","timestamp":"2025-06-05 09:20:27"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx48405lvg4hxabjf4igx for the discountId cmbhxr0en0000hxngzzwgceol by cosmin.oprea","timestamp":"2025-06-05 09:20:27"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-05 09:20:28"}
{"level":"info","message":"Price changed for 11531743192: 179.35 -> 211","timestamp":"2025-06-05 09:20:28"}
{"level":"info","message":"Created price history record for 11531743192","timestamp":"2025-06-05 09:20:28"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41905k9g4hxebqu78vr for the discountId cmbhxr0en0000hxngzzwgceol by cosmin.oprea","timestamp":"2025-06-05 09:20:28"}
{"level":"info","message":"FIXED_AMOUNT: Base price 211 - discount 15 = final price 196","timestamp":"2025-06-05 09:24:30"}
{"level":"info","message":"FIXED_AMOUNT: Base price 211 - discount 15 = final price 196","timestamp":"2025-06-05 09:25:50"}
{"level":"info","message":"FIXED_AMOUNT: Base price 211 - discount 15 = final price 196","timestamp":"2025-06-05 09:25:57"}
{"level":"info","message":"FIXED_AMOUNT: Base price 211 - discount 1 = final price 210","timestamp":"2025-06-05 09:27:27"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbhwx41905k9g4hxebqu78vr (11531743192)","timestamp":"2025-06-05 09:40:15"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx41905k9g4hxebqu78vr","timestamp":"2025-06-05 09:40:15"}
{"level":"info","message":"Found product 11531743192 with base price 211","timestamp":"2025-06-05 09:40:15"}
{"level":"info","message":"Found active discount for 11531743192: NEW_PRICE with value 99.99","timestamp":"2025-06-05 09:40:15"}
{"level":"info","message":"NEW_PRICE: New price set to 99.99","timestamp":"2025-06-05 09:40:15"}
{"level":"info","message":"Discount applied: 11531743192 - Original: 211, Final: 99.99, Discount %: 52.611374407582938389","timestamp":"2025-06-05 09:40:15"}
{"level":"info","message":"Price changed for 11531743192: 211 -> 99.99","timestamp":"2025-06-05 09:40:15"}
{"level":"info","message":"Updated product 11531743192 with new price 99.99","timestamp":"2025-06-05 09:40:15"}
{"level":"info","message":"Created price history record for 11531743192","timestamp":"2025-06-05 09:40:15"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-05 09:40:15"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxsh5h0006hxngai17nk9l updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:40:15"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbhwx48405lvg4hxabjf4igx (12317501755)","timestamp":"2025-06-05 09:40:18"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx48405lvg4hxabjf4igx","timestamp":"2025-06-05 09:40:18"}
{"level":"info","message":"Found product 12317501755 with base price 55","timestamp":"2025-06-05 09:40:18"}
{"level":"info","message":"Found active discount for 12317501755: NEW_PRICE with value 99.99","timestamp":"2025-06-05 09:40:18"}
{"level":"info","message":"Discount not applied: 12317501755 - Final price would not be lower","timestamp":"2025-06-05 09:40:18"}
{"level":"info","message":"No price change for 12317501755: 55","timestamp":"2025-06-05 09:40:18"}
{"level":"info","message":"Updated product 12317501755 with new price 55","timestamp":"2025-06-05 09:40:18"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-05 09:40:18"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxsh5h0006hxngai17nk9l updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:40:18"}
{"level":"error","message":"DiscountAddProductAction -> Product \"11531743192\" already has a discount by cosmin.oprea","timestamp":"2025-06-05 09:40:30"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx41905k9g4hxebqu78vr","timestamp":"2025-06-05 09:40:37"}
{"level":"info","message":"Found product 11531743192 with base price 211","timestamp":"2025-06-05 09:40:37"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-05 09:40:37"}
{"level":"info","message":"Price changed for 11531743192: 99.99 -> 211","timestamp":"2025-06-05 09:40:37"}
{"level":"info","message":"Updated product 11531743192 with new price 211","timestamp":"2025-06-05 09:40:37"}
{"level":"info","message":"Created price history record for 11531743192","timestamp":"2025-06-05 09:40:37"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41905k9g4hxebqu78vr for the discountId cmbhxsh5h0006hxngai17nk9l by cosmin.oprea","timestamp":"2025-06-05 09:40:37"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx48405lvg4hxabjf4igx","timestamp":"2025-06-05 09:40:39"}
{"level":"info","message":"Found product 12317501755 with base price 55","timestamp":"2025-06-05 09:40:39"}
{"level":"info","message":"No active discount found for product 12317501755","timestamp":"2025-06-05 09:40:39"}
{"level":"info","message":"No price change for 12317501755: 55","timestamp":"2025-06-05 09:40:39"}
{"level":"info","message":"Updated product 12317501755 with new price 55","timestamp":"2025-06-05 09:40:39"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx48405lvg4hxabjf4igx for the discountId cmbhxsh5h0006hxngai17nk9l by cosmin.oprea","timestamp":"2025-06-05 09:40:39"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbhwx41905k9g4hxebqu78vr (11531743192)","timestamp":"2025-06-05 09:41:21"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx41905k9g4hxebqu78vr","timestamp":"2025-06-05 09:41:21"}
{"level":"info","message":"Found product 11531743192 with base price 211","timestamp":"2025-06-05 09:41:21"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-05 09:41:21"}
{"level":"info","message":"No price change for 11531743192: 211","timestamp":"2025-06-05 09:41:21"}
{"level":"info","message":"Updated product 11531743192 with new price 211","timestamp":"2025-06-05 09:41:21"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-05 09:41:21"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxrq0r0003hxnggwgyaasr updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:41:21"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbhwx48405lvg4hxabjf4igx (12317501755)","timestamp":"2025-06-05 09:41:23"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx48405lvg4hxabjf4igx","timestamp":"2025-06-05 09:41:23"}
{"level":"info","message":"Found product 12317501755 with base price 55","timestamp":"2025-06-05 09:41:23"}
{"level":"info","message":"No active discount found for product 12317501755","timestamp":"2025-06-05 09:41:23"}
{"level":"info","message":"No price change for 12317501755: 55","timestamp":"2025-06-05 09:41:23"}
{"level":"info","message":"Updated product 12317501755 with new price 55","timestamp":"2025-06-05 09:41:23"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-05 09:41:23"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxrq0r0003hxnggwgyaasr updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:41:23"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx41905k9g4hxebqu78vr","timestamp":"2025-06-05 09:41:41"}
{"level":"info","message":"Found product 11531743192 with base price 211","timestamp":"2025-06-05 09:41:41"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-05 09:41:41"}
{"level":"info","message":"No price change for 11531743192: 211","timestamp":"2025-06-05 09:41:41"}
{"level":"info","message":"Updated product 11531743192 with new price 211","timestamp":"2025-06-05 09:41:41"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41905k9g4hxebqu78vr for the discountId cmbhxrq0r0003hxnggwgyaasr by cosmin.oprea","timestamp":"2025-06-05 09:41:41"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx48405lvg4hxabjf4igx","timestamp":"2025-06-05 09:41:43"}
{"level":"info","message":"Found product 12317501755 with base price 55","timestamp":"2025-06-05 09:41:43"}
{"level":"info","message":"No active discount found for product 12317501755","timestamp":"2025-06-05 09:41:43"}
{"level":"info","message":"No price change for 12317501755: 55","timestamp":"2025-06-05 09:41:43"}
{"level":"info","message":"Updated product 12317501755 with new price 55","timestamp":"2025-06-05 09:41:43"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx48405lvg4hxabjf4igx for the discountId cmbhxrq0r0003hxnggwgyaasr by cosmin.oprea","timestamp":"2025-06-05 09:41:43"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbhwx41905k9g4hxebqu78vr (11531743192)","timestamp":"2025-06-05 09:41:58"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx41905k9g4hxebqu78vr","timestamp":"2025-06-05 09:41:58"}
{"level":"info","message":"Found product 11531743192 with base price 211","timestamp":"2025-06-05 09:41:58"}
{"level":"info","message":"Found active discount for 11531743192: PERCENTAGE with value 15","timestamp":"2025-06-05 09:41:58"}
{"level":"info","message":"PERCENTAGE: 211 - 15% = 179.35","timestamp":"2025-06-05 09:41:58"}
{"level":"info","message":"Discount applied: 11531743192 - Original: 211, Final: 179.35, Discount %: 15","timestamp":"2025-06-05 09:41:58"}
{"level":"info","message":"Price changed for 11531743192: 211 -> 179.35","timestamp":"2025-06-05 09:41:58"}
{"level":"info","message":"Updated product 11531743192 with new price 179.35","timestamp":"2025-06-05 09:41:58"}
{"level":"info","message":"Created price history record for 11531743192","timestamp":"2025-06-05 09:41:58"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-05 09:41:58"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxr0en0000hxngzzwgceol updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:41:58"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbhwx48405lvg4hxabjf4igx (12317501755)","timestamp":"2025-06-05 09:42:01"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx48405lvg4hxabjf4igx","timestamp":"2025-06-05 09:42:01"}
{"level":"info","message":"Found product 12317501755 with base price 55","timestamp":"2025-06-05 09:42:01"}
{"level":"info","message":"Found active discount for 12317501755: PERCENTAGE with value 15","timestamp":"2025-06-05 09:42:01"}
{"level":"info","message":"PERCENTAGE: 55 - 15% = 46.75","timestamp":"2025-06-05 09:42:01"}
{"level":"info","message":"Discount applied: 12317501755 - Original: 55, Final: 46.75, Discount %: 15","timestamp":"2025-06-05 09:42:01"}
{"level":"info","message":"Price changed for 12317501755: 55 -> 46.75","timestamp":"2025-06-05 09:42:01"}
{"level":"info","message":"Updated product 12317501755 with new price 46.75","timestamp":"2025-06-05 09:42:01"}
{"level":"info","message":"Created price history record for 12317501755","timestamp":"2025-06-05 09:42:01"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-05 09:42:01"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxr0en0000hxngzzwgceol updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:42:01"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx41905k9g4hxebqu78vr","timestamp":"2025-06-05 09:42:08"}
{"level":"info","message":"Found product 11531743192 with base price 211","timestamp":"2025-06-05 09:42:08"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-05 09:42:08"}
{"level":"info","message":"Price changed for 11531743192: 179.35 -> 211","timestamp":"2025-06-05 09:42:08"}
{"level":"info","message":"Updated product 11531743192 with new price 211","timestamp":"2025-06-05 09:42:08"}
{"level":"info","message":"Created price history record for 11531743192","timestamp":"2025-06-05 09:42:08"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41905k9g4hxebqu78vr for the discountId cmbhxr0en0000hxngzzwgceol by cosmin.oprea","timestamp":"2025-06-05 09:42:08"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx48405lvg4hxabjf4igx","timestamp":"2025-06-05 09:42:10"}
{"level":"info","message":"Found product 12317501755 with base price 55","timestamp":"2025-06-05 09:42:10"}
{"level":"info","message":"No active discount found for product 12317501755","timestamp":"2025-06-05 09:42:10"}
{"level":"info","message":"Price changed for 12317501755: 46.75 -> 55","timestamp":"2025-06-05 09:42:10"}
{"level":"info","message":"Updated product 12317501755 with new price 55","timestamp":"2025-06-05 09:42:10"}
{"level":"info","message":"Created price history record for 12317501755","timestamp":"2025-06-05 09:42:10"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx48405lvg4hxabjf4igx for the discountId cmbhxr0en0000hxngzzwgceol by cosmin.oprea","timestamp":"2025-06-05 09:42:10"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbhwx41905k9g4hxebqu78vr (11531743192)","timestamp":"2025-06-05 09:43:13"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx41905k9g4hxebqu78vr","timestamp":"2025-06-05 09:43:13"}
{"level":"info","message":"Found product 11531743192 with base price 211","timestamp":"2025-06-05 09:43:13"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-05 09:43:13"}
{"level":"info","message":"No price change for 11531743192: 211","timestamp":"2025-06-05 09:43:13"}
{"level":"info","message":"Updated product 11531743192 with new price 211","timestamp":"2025-06-05 09:43:13"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-05 09:43:13"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxrq0r0003hxnggwgyaasr updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:43:13"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx41905k9g4hxebqu78vr","timestamp":"2025-06-05 09:44:01"}
{"level":"info","message":"Found product 11531743192 with base price 211","timestamp":"2025-06-05 09:44:01"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-05 09:44:01"}
{"level":"info","message":"No price change for 11531743192: 211","timestamp":"2025-06-05 09:44:01"}
{"level":"info","message":"Updated product 11531743192 with new price 211","timestamp":"2025-06-05 09:44:01"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41905k9g4hxebqu78vr for the discountId cmbhxrq0r0003hxnggwgyaasr by cosmin.oprea","timestamp":"2025-06-05 09:44:01"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbhwx41905k9g4hxebqu78vr (11531743192)","timestamp":"2025-06-05 09:44:08"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx41905k9g4hxebqu78vr","timestamp":"2025-06-05 09:44:08"}
{"level":"info","message":"Found product 11531743192 with base price 211","timestamp":"2025-06-05 09:44:08"}
{"level":"info","message":"Found active discount for 11531743192: NEW_PRICE with value 99.99","timestamp":"2025-06-05 09:44:08"}
{"level":"info","message":"NEW_PRICE: New price set to 99.99","timestamp":"2025-06-05 09:44:08"}
{"level":"info","message":"Discount applied: 11531743192 - Original: 211, Final: 99.99, Discount %: 52.611374407582938389","timestamp":"2025-06-05 09:44:08"}
{"level":"info","message":"Price changed for 11531743192: 211 -> 99.99","timestamp":"2025-06-05 09:44:08"}
{"level":"info","message":"Updated product 11531743192 with new price 99.99","timestamp":"2025-06-05 09:44:08"}
{"level":"info","message":"Created price history record for 11531743192","timestamp":"2025-06-05 09:44:08"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-05 09:44:08"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxsh5h0006hxngai17nk9l updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:44:08"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx41905k9g4hxebqu78vr","timestamp":"2025-06-05 09:44:43"}
{"level":"info","message":"Found product 11531743192 with base price 211","timestamp":"2025-06-05 09:44:43"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-05 09:44:43"}
{"level":"info","message":"Price changed for 11531743192: 99.99 -> 211","timestamp":"2025-06-05 09:44:43"}
{"level":"info","message":"Updated product 11531743192 with new price 211","timestamp":"2025-06-05 09:44:43"}
{"level":"info","message":"Created price history record for 11531743192","timestamp":"2025-06-05 09:44:43"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41905k9g4hxebqu78vr for the discountId cmbhxsh5h0006hxngai17nk9l by cosmin.oprea","timestamp":"2025-06-05 09:44:43"}
{"level":"info","message":"FIXED_AMOUNT: 211 - 1 = 210","timestamp":"2025-06-05 09:45:09"}
{"level":"info","message":"FIXED_AMOUNT: 211 - 1 = 210","timestamp":"2025-06-05 09:45:12"}
{"level":"info","message":"PERCENTAGE: 211 - 1% = 208.89","timestamp":"2025-06-05 09:45:22"}
{"level":"info","message":"NEW_PRICE: New price set to 1","timestamp":"2025-06-05 09:45:28"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbhwx41905k9g4hxebqu78vr (11531743192)","timestamp":"2025-06-05 09:51:16"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx41905k9g4hxebqu78vr","timestamp":"2025-06-05 09:51:16"}
{"level":"info","message":"Found product 11531743192 with base price 211","timestamp":"2025-06-05 09:51:16"}
{"level":"info","message":"Found active discount for 11531743192: NEW_PRICE with value 99.99","timestamp":"2025-06-05 09:51:16"}
{"level":"info","message":"NEW_PRICE: New price set to 99.99","timestamp":"2025-06-05 09:51:16"}
{"level":"info","message":"Discount applied: 11531743192 - Original: 211, Final: 99.99, Discount %: 52.611374407582938389","timestamp":"2025-06-05 09:51:16"}
{"level":"info","message":"Price changed for 11531743192: 211 -> 99.99","timestamp":"2025-06-05 09:51:16"}
{"level":"info","message":"Updated product 11531743192 with new price 99.99","timestamp":"2025-06-05 09:51:16"}
{"level":"info","message":"Created price history record for 11531743192","timestamp":"2025-06-05 09:51:16"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-05 09:51:16"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxsh5h0006hxngai17nk9l updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:51:16"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx41905k9g4hxebqu78vr","timestamp":"2025-06-05 09:51:19"}
{"level":"info","message":"Found product 11531743192 with base price 211","timestamp":"2025-06-05 09:51:19"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-05 09:51:19"}
{"level":"info","message":"Price changed for 11531743192: 99.99 -> 211","timestamp":"2025-06-05 09:51:19"}
{"level":"info","message":"Updated product 11531743192 with new price 211","timestamp":"2025-06-05 09:51:19"}
{"level":"info","message":"Created price history record for 11531743192","timestamp":"2025-06-05 09:51:19"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41905k9g4hxebqu78vr for the discountId cmbhxsh5h0006hxngai17nk9l by cosmin.oprea","timestamp":"2025-06-05 09:51:19"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbhwx41905k9g4hxebqu78vr (11531743192)","timestamp":"2025-06-05 09:51:30"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx41905k9g4hxebqu78vr","timestamp":"2025-06-05 09:51:30"}
{"level":"info","message":"Found product 11531743192 with base price 211","timestamp":"2025-06-05 09:51:30"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-05 09:51:30"}
{"level":"info","message":"No price change for 11531743192: 211","timestamp":"2025-06-05 09:51:30"}
{"level":"info","message":"Updated product 11531743192 with new price 211","timestamp":"2025-06-05 09:51:30"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-05 09:51:30"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxrq0r0003hxnggwgyaasr updated successfully by cosmin.oprea","timestamp":"2025-06-05 09:51:30"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx41905k9g4hxebqu78vr","timestamp":"2025-06-05 09:51:33"}
{"level":"info","message":"Found product 11531743192 with base price 211","timestamp":"2025-06-05 09:51:33"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-05 09:51:33"}
{"level":"info","message":"No price change for 11531743192: 211","timestamp":"2025-06-05 09:51:33"}
{"level":"info","message":"Updated product 11531743192 with new price 211","timestamp":"2025-06-05 09:51:33"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41905k9g4hxebqu78vr for the discountId cmbhxrq0r0003hxnggwgyaasr by cosmin.oprea","timestamp":"2025-06-05 09:51:33"}
{"level":"info","message":"FIXED_AMOUNT: 211 - 1 = 210","timestamp":"2025-06-05 10:03:27"}
{"level":"info","message":"FIXED_AMOUNT: 211 - 1 = 210","timestamp":"2025-06-05 10:06:13"}
{"level":"info","message":"FIXED_AMOUNT: 211 - 1 = 210","timestamp":"2025-06-05 10:06:16"}
{"level":"info","message":"FIXED_AMOUNT: 211 - 1 = 210","timestamp":"2025-06-05 10:06:17"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbhwx41905k9g4hxebqu78vr (11531743192)","timestamp":"2025-06-05 10:06:21"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx41905k9g4hxebqu78vr","timestamp":"2025-06-05 10:06:21"}
{"level":"info","message":"Found product 11531743192 with base price 211","timestamp":"2025-06-05 10:06:21"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-05 10:06:21"}
{"level":"info","message":"No price change for 11531743192: 211","timestamp":"2025-06-05 10:06:21"}
{"level":"info","message":"Updated product 11531743192 with new price 211","timestamp":"2025-06-05 10:06:21"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-05 10:06:21"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxrq0r0003hxnggwgyaasr updated successfully by cosmin.oprea","timestamp":"2025-06-05 10:06:21"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx41905k9g4hxebqu78vr","timestamp":"2025-06-05 10:07:15"}
{"level":"info","message":"Found product 11531743192 with base price 211","timestamp":"2025-06-05 10:07:15"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-05 10:07:15"}
{"level":"info","message":"No price change for 11531743192: 211","timestamp":"2025-06-05 10:07:15"}
{"level":"info","message":"Updated product 11531743192 with new price 211","timestamp":"2025-06-05 10:07:15"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41905k9g4hxebqu78vr for the discountId cmbhxrq0r0003hxnggwgyaasr by cosmin.oprea","timestamp":"2025-06-05 10:07:15"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbhwx41905k9g4hxebqu78vr (11531743192)","timestamp":"2025-06-05 10:07:20"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx41905k9g4hxebqu78vr","timestamp":"2025-06-05 10:07:20"}
{"level":"info","message":"Found product 11531743192 with base price 211","timestamp":"2025-06-05 10:07:20"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-05 10:07:20"}
{"level":"info","message":"No price change for 11531743192: 211","timestamp":"2025-06-05 10:07:20"}
{"level":"info","message":"Updated product 11531743192 with new price 211","timestamp":"2025-06-05 10:07:20"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-05 10:07:20"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbhxrq0r0003hxnggwgyaasr updated successfully by cosmin.oprea","timestamp":"2025-06-05 10:07:20"}
{"level":"info","message":"Discount with name suma fixa 2 created successfully by cosmin.oprea","timestamp":"2025-06-05 10:10:30"}
{"level":"error","message":"DiscountAddProductAction -> Product \"11531743192\" already has a discount by cosmin.oprea","timestamp":"2025-06-05 10:10:46"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx41905k9g4hxebqu78vr","timestamp":"2025-06-05 10:10:52"}
{"level":"info","message":"Found product 11531743192 with base price 211","timestamp":"2025-06-05 10:10:52"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-05 10:10:52"}
{"level":"info","message":"No price change for 11531743192: 211","timestamp":"2025-06-05 10:10:52"}
{"level":"info","message":"Updated product 11531743192 with new price 211","timestamp":"2025-06-05 10:10:52"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx41905k9g4hxebqu78vr for the discountId cmbhxrq0r0003hxnggwgyaasr by cosmin.oprea","timestamp":"2025-06-05 10:10:52"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbhwx41905k9g4hxebqu78vr (11531743192)","timestamp":"2025-06-05 10:10:57"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx41905k9g4hxebqu78vr","timestamp":"2025-06-05 10:10:57"}
{"level":"info","message":"Found product 11531743192 with base price 211","timestamp":"2025-06-05 10:10:57"}
{"level":"info","message":"Found active discount for 11531743192: FIXED_AMOUNT with value 1","timestamp":"2025-06-05 10:10:57"}
{"level":"info","message":"FIXED_AMOUNT: 211 - 1 = 210","timestamp":"2025-06-05 10:10:57"}
{"level":"info","message":"Discount applied: 11531743192 - Original: 211, Final: 210, Discount %: 0.47393364928909952607","timestamp":"2025-06-05 10:10:57"}
{"level":"info","message":"Price changed for 11531743192: 211 -> 210","timestamp":"2025-06-05 10:10:57"}
{"level":"info","message":"Updated product 11531743192 with new price 210","timestamp":"2025-06-05 10:10:58"}
{"level":"info","message":"Created price history record for 11531743192","timestamp":"2025-06-05 10:10:58"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-05 10:10:58"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbj1eiy0007ehx8ccviq5fdz updated successfully by cosmin.oprea","timestamp":"2025-06-05 10:10:58"}
{"level":"info","message":"Discount with name Activation created successfully by cosmin.oprea","timestamp":"2025-06-05 10:26:56"}
{"level":"info","message":"Discount with name xxx created successfully by cosmin.oprea","timestamp":"2025-06-05 10:27:24"}
{"level":"info","message":"Discount with name xxxxx created successfully by cosmin.oprea","timestamp":"2025-06-05 10:31:13"}
{"level":"info","message":"Discount with name xxx updated successfully by cosmin.oprea","timestamp":"2025-06-05 10:39:48"}
{"level":"info","message":"ProductSearchAction -> Search for 11531743192 with success by cosmin.oprea","timestamp":"2025-06-05 10:45:03"}
{"level":"info","message":"getProductAttributes -> Product \"11531743192\" has the attributes:  by cosmin.oprea","timestamp":"2025-06-05 10:46:41"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11531743192\" with success by cosmin.oprea","timestamp":"2025-06-05 10:46:41"}
{"level":"info","message":"Starting expired discount check at 2025-06-05T08:03:57.848Z","timestamp":"2025-06-05 11:03:57"}
{"level":"info","message":"Found 3 expired discounts to process","timestamp":"2025-06-05 11:03:57"}
{"level":"info","message":"Updating prices for 0 products from expired discount cmbj1znzp007phx8ca5jkeoc2","timestamp":"2025-06-05 11:03:57"}
{"level":"info","message":"Updating prices for 0 products from expired discount cmbj2563j007vhx8cfds9amsb","timestamp":"2025-06-05 11:03:57"}
{"level":"info","message":"Updating prices for 0 products from expired discount cmbj208zk007shx8cd495ymq6","timestamp":"2025-06-05 11:03:57"}
{"level":"info","message":"Discount with name suma fixa 2 updated successfully by cosmin.oprea","timestamp":"2025-06-05 11:04:38"}
{"level":"info","message":"Starting expired discount check at 2025-06-05T08:05:32.751Z","timestamp":"2025-06-05 11:05:32"}
{"level":"info","message":"Found 1 expired discounts to process","timestamp":"2025-06-05 11:05:32"}
{"level":"info","message":"Updating prices for 1 products from expired discount cmbj1eiy0007ehx8ccviq5fdz","timestamp":"2025-06-05 11:05:32"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx41905k9g4hxebqu78vr","timestamp":"2025-06-05 11:05:32"}
{"level":"info","message":"Found product 11531743192 with base price 211","timestamp":"2025-06-05 11:05:32"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-05 11:05:32"}
{"level":"info","message":"Price changed for 11531743192: 210 -> 211","timestamp":"2025-06-05 11:05:32"}
{"level":"info","message":"Updated product 11531743192 with new price 211","timestamp":"2025-06-05 11:05:32"}
{"level":"info","message":"Created price history record for 11531743192","timestamp":"2025-06-05 11:05:32"}
{"level":"info","message":"Starting expired discount check at 2025-06-05T08:07:30.079Z","timestamp":"2025-06-05 11:07:30"}
{"level":"info","message":"Found 0 expired discounts to process","timestamp":"2025-06-05 11:07:30"}
{"level":"info","message":"Discount with name suma fixa 2 updated successfully by cosmin.oprea","timestamp":"2025-06-05 11:07:50"}
{"level":"info","message":"Discount with name suma fixa 2 updated successfully by cosmin.oprea","timestamp":"2025-06-05 11:07:55"}
{"level":"info","message":"Starting expired discount check at 2025-06-05T08:08:46.594Z","timestamp":"2025-06-05 11:08:46"}
{"level":"info","message":"Found 1 expired discounts to process","timestamp":"2025-06-05 11:08:46"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx41905k9g4hxebqu78vr","timestamp":"2025-06-05 11:08:46"}
{"level":"info","message":"Found product 11531743192 with base price 211","timestamp":"2025-06-05 11:08:46"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-05 11:08:46"}
{"level":"info","message":"No price change for 11531743192: 211","timestamp":"2025-06-05 11:08:46"}
{"level":"info","message":"Updated product 11531743192 with new price 211","timestamp":"2025-06-05 11:08:46"}
{"level":"info","message":"Processed expired discount cmbj1eiy0007ehx8ccviq5fdz: deactivated and removed 1 products","timestamp":"2025-06-05 11:08:46"}
{"level":"info","message":"ProductSearchAction -> Search for 11531743192 with success by cosmin.oprea","timestamp":"2025-06-05 11:08:58"}
{"level":"info","message":"getProductAttributes -> Product \"11531743192\" has the attributes:  by cosmin.oprea","timestamp":"2025-06-05 11:45:43"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11531743192\" with success by cosmin.oprea","timestamp":"2025-06-05 11:45:43"}
{"level":"info","message":"getProductAttributes -> Product \"11531743192\" has the attributes:  by cosmin.oprea","timestamp":"2025-06-05 12:30:00"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11531743192\" with success by cosmin.oprea","timestamp":"2025-06-05 12:30:00"}
{"level":"info","message":"ProductAddAttributeAction -> Attribute \"mac\" added successfully for product 11531743192 by cosmin.oprea","timestamp":"2025-06-05 12:30:05"}
{"level":"info","message":"getProductAttributes -> Product \"11531743192\" has the attributes: mac -- reasd by cosmin.oprea","timestamp":"2025-06-05 12:30:05"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11531743192\" with success by cosmin.oprea","timestamp":"2025-06-05 12:30:05"}
{"level":"error","message":"getProductDetails -> Error at parsing: [\n  {\n    \"code\": \"too_big\",\n    \"maximum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-06-05 12:30:59"}
{"level":"error","message":"getProductDetails -> Error at parsing: [\n  {\n    \"code\": \"too_big\",\n    \"maximum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-06-05 12:31:13"}
{"level":"info","message":"getProductAttributes -> Product \"11531743192\" has the attributes: mac -- reasd by cosmin.oprea","timestamp":"2025-06-05 12:37:43"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11531743192\" with success by cosmin.oprea","timestamp":"2025-06-05 12:37:43"}
{"level":"error","message":"ProductSearchAction -> Error at parsing: [\n  {\n    \"code\": \"too_big\",\n    \"maximum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-06-05 12:49:11"}
{"level":"error","message":"ProductSearchAction -> Error at parsing: [\n  {\n    \"code\": \"too_big\",\n    \"maximum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-06-05 12:49:24"}
{"level":"error","message":"ProductSearchAction -> Error at parsing: [\n  {\n    \"code\": \"too_big\",\n    \"maximum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-06-05 12:59:12"}
{"level":"error","message":"getProductDetails -> Error at parsing: [\n  {\n    \"code\": \"too_big\",\n    \"maximum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-06-05 13:35:24"}
{"level":"error","message":"getProductDetails -> Error at parsing: [\n  {\n    \"code\": \"too_big\",\n    \"maximum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-06-05 13:36:14"}
{"level":"error","message":"getProductDetails -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-06-05 13:36:18"}
{"level":"info","message":"Discount with name xxx updated successfully by cosmin.oprea","timestamp":"2025-06-05 13:38:57"}
{"level":"info","message":"Discount with name xxx updated successfully by cosmin.oprea","timestamp":"2025-06-05 13:39:20"}
{"level":"info","message":"Starting expired discount check at 2025-06-05T10:40:38.296Z","timestamp":"2025-06-05 13:40:38"}
{"level":"info","message":"Found 0 expired discounts to process","timestamp":"2025-06-05 13:40:38"}
{"level":"info","message":"Starting expired discount check at 2025-06-05T10:40:52.282Z","timestamp":"2025-06-05 13:40:52"}
{"level":"info","message":"Found 0 expired discounts to process","timestamp":"2025-06-05 13:40:52"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:02:25"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:02:25"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:10"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:10"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:10"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:10"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:14"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:14"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:14"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:14"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:14"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:14"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:15"}
{"level":"error","message":"User with ID asd not found: [object Object]","timestamp":"2025-06-05 17:03:15"}
{"level":"error","message":"User with ID create not found: [object Object]","timestamp":"2025-06-05 17:04:29"}
{"level":"error","message":"User with ID create not found: [object Object]","timestamp":"2025-06-05 17:04:29"}
