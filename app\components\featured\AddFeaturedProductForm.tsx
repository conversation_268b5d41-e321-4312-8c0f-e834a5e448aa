"use client";

import { useState, useTransition } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { Search } from "lucide-react";
import { toggleFeaturedProduct } from "@/app/actions/featuredActions";

export default function AddFeaturedProductForm(  ) {
  const [materialNumber, setMaterialNumber] = useState("");
  const [isPending, startTransition] = useTransition();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!materialNumber.trim()) {
      toast.error("Please enter a material number");
      return;
    }
    
    startTransition(async () => {
      try {
        // Call the action to toggle featured status
        const result = await toggleFeaturedProduct(materialNumber);
        
        if (result.status === "SUCCESS") {
          toast.success(result.message);
          setMaterialNumber(""); // Clear the input on success
        } else {
          toast.error(result.message);
        }
      } catch (error) {
        toast.error("An error occurred while adding the product");
      }
    });
  };

  return (
    <form onSubmit={handleSubmit} className="flex gap-2 mb-6">
      <div className="flex-1">
        <Input
          type="text"
          placeholder="Enter material number"
          value={materialNumber}
          onChange={(e) => setMaterialNumber(e.target.value)}
          disabled={isPending}
        />
      </div>
      <Button type="submit" disabled={isPending}>
        {isPending ? "Adding..." : (
          <>
            <Search className="h-4 w-4 mr-2" />
            Add to Featured
          </>
        )}
      </Button>
    </form>
  );
}