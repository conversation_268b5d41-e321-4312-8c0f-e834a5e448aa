"use server";

import { DiscountAddProductToFormValues, DiscountFormValues, DiscountAddProductSchema, 
  createDiscountSchema, deleteProductFromDiscountSchema, processCSVDiscountSchema, updateDiscountSchema, 
  DiscountDeleteProductFormValues, DiscountProcessCSVFormValues, DiscountDeleteAllProductsFormValues, DiscountDeleteAllProductsSchema,
   ProductSearchSchema, 
   ProductSearchFormValues,
   ProductAddAttributeFormValues,
   ProductDeleteAttributeSchema,
   ProductDeleteAttributeSchemaFormValues,
   ProductAddAttributeSchema,
   ProductAddAttributeArraySchema} from "../zod/zod";
import { revalidatePath } from "next/cache";
import prisma from "../utils/db";
import { logError, logInfo } from "@/lib/logger";
import { PriceHistory, ProductHistory } from "@/generated/prisma";
import { Decimal } from "@/generated/prisma/runtime/library";
import { calculateDiscountPercentage, calculateFinalPrice, updateProductPrice } from "../utils/utils";
import { requireAdminOrModerator } from "@/lib/auth-utils";


interface ReturnActionWithFields {
  status: "SUCCESS" | "ERROR";
  message: string;
  fieldErrors?: Record<string, string>;
}

interface ReturnActionWithArray {
  status: "SUCCESS" | "ERROR";
  message: string;
  invalidProducts?: string[]
}

export interface ProductDetailsInterface {
    id: string;
    Material_Number: string;
    Material_Group: string | null;
    Net_Weight: string | null;
    Description_Local: string | null;
    PretAM: string  | null;
    FinalPrice: string | null;
    HasDiscount: boolean;
    activeDiscountType: string | null;
    activeDiscountValue: string | null;
    Base_Unit_Of_Measur: string | null;
    Cross_Plant: string | null;
    New_Material: string | null;
    createdAt: Date;
    last_updated_at: Date;
}

interface ReturnActionWithProduct {
  status: "SUCCESS" | "ERROR";
  message: string;
  productDetails?: ProductDetailsInterface;
  productHistory?: ProductHistory[];
  priceHistory?: PriceHistory[];
}

export interface ReturnAction {
  status: "SUCCESS" | "ERROR";
  message: string;
}

export async function DiscountCreateAction(data: DiscountFormValues): Promise<ReturnActionWithFields> {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  try{
    const parsed = createDiscountSchema.safeParse(data)

    if (!parsed.success) {
      const fieldErrors = Object.fromEntries(
        Object.entries(parsed.error.flatten().fieldErrors).map(([key, value]) => [key, value?.[0] || ""])
      );
      logError(`DiscountCreateAction -> Error at parsing: ${parsed.error} by ${actor.email}`)
      return {
        status: "ERROR",
        message: "Validation error",
        fieldErrors
      };
    }

    const { name, description, type, value, startDate, endDate, active } = parsed.data;

    // Additional validation for dates
    const now = new Date();
    if (endDate < now) {
      logError(`DiscountCreateAction -> End date is in the past: ${endDate.toISOString()} by ${userEmail}`)
      return {
        status: "ERROR",
        message: "End date cannot be in the past",
        fieldErrors: {
          endDate: "End date cannot be in the past",
        },
      };
    }

    const discountExists = await prisma.discount.findUnique({
      where: { name },
    });

    if (discountExists) {
      logError(`DiscountCreateAction -> A discount with this name already exists ${name} by ${userEmail}`)
      return {
        status: "ERROR",
        message: "A discount with this name already exists.",
        fieldErrors: {
          name: "A discount with this name already exists.",
        },
      };
    }

    try{
      await prisma.$transaction(async (tx) => {
        const discount = await tx.discount.create({
          data: {
            name,
            type,
            description,
            value: value,
            startDate: startDate,
            endDate: endDate,
            active: active,
            createdBy: userEmail,
          },
        });

        await tx.discountHistory.create({
          data: {
            change_type: "INSERT",
            changes: {},
            changed_by: userEmail,
            snapshot: {
                active: discount.active,
                description: discount.description,
                value: discount.value,
                startDate: discount.startDate,
                endDate: discount.endDate,
                createdBy: discount.createdBy,
                name: discount.name,
                id: discount.id, // Include ID if it's not already in snapshot
            },
            version: 1,
            discountId: discount.id
          }
        })

      });

        logInfo(`Discount with name ${name} created successfully by ${userEmail}`);
      return{
          status: "SUCCESS",
          message: "Discount created with success"
      }
    }catch(e){
        logError(`DiscountCreateAction -> Database error: ${e}  by ${userEmail}`);
      return {
        status: "ERROR",
        message: "Database error occurred while creating the discount.",
      };
    }

  }catch(e){
      logError(`DiscountCreateAction -> Server error: ${e}  by ${userEmail}`);
    return{
      status: "ERROR",
      message: "Unexpected server error. Please try again later.",
    }
  }
}

export async function DiscountUpdateAction(data: DiscountFormValues): Promise<ReturnActionWithFields> {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  try {
    const parsed = updateDiscountSchema.safeParse(data);

    if (!parsed.success) {
      const fieldErrors = Object.fromEntries(
        Object.entries(parsed.error.flatten().fieldErrors).map(([key, value]) => [key, value?.[0] || ""])
      );
        logError(`DiscountUpdateAction -> Error at parsing: ${parsed.error} by ${userEmail}`)
      return {
        status: "ERROR",
        message: "Validation error",
        fieldErrors
      };
    }

    const { id, name, description, type, value, startDate, endDate, active } = parsed.data;

    const discountExists = await prisma.discount.findUnique({
      where: { id },
    });

    if (!discountExists) {
        logError(`DiscountUpdateAction -> Discount not found: ${id} by ${userEmail}`)
      return {
        status: "ERROR",
        message: "Discount not found",
      };
    }

    type TrackedDiscountFields = "name" | "description" | "type" | "value" | "startDate" | "endDate" | "active";
    type Changes = Record<
      TrackedDiscountFields,
      { old: string | number | Date | Decimal | boolean; new: string | number | Date | Decimal | boolean } // Include Decimal
    >;
    const changes: Partial<Changes> = {};
    const fieldsToCompare: TrackedDiscountFields[] = [
      "name", "description", "type", "value", "startDate", "endDate" , "active"
    ];

    for (const key of fieldsToCompare) {
      const oldVal = discountExists[key];
      const newVal = parsed.data[key];

      // Comparison logic - see dedicated section below for improvements
      let isDifferent = false;
      if (oldVal instanceof Decimal && newVal instanceof Decimal) {
          isDifferent = !oldVal.equals(newVal);
      } else if (oldVal instanceof Date && newVal instanceof Date) {
          isDifferent = oldVal.getTime() !== newVal.getTime();
      } else {
          isDifferent = oldVal !== newVal;
      }

      if (isDifferent) {
        changes[key] = {
          old: oldVal, // Keep original types, convert to human-readable in presentation layer
          new: newVal,
        };
      }
    }

    try{

        // Begin a Prisma transaction
     const updatedDiscount = await prisma.$transaction(async (tx) => {
      const discount = await tx.discount.update({
        where: { id },
        data: {
          name,
          description,
          type,
          value,
          startDate: startDate,
          endDate: endDate,
          active: active,
          createdBy: userEmail
        },
      });

      const latestVersion = await tx.discountHistory.findFirst({
        where: { discountId: id },
        orderBy: { version: "desc" },
        select: { version: true },
      });

      await tx.discountHistory.create({
        data: {
          change_type: "UPDATE",
          changes,
          changed_by: userEmail,
          snapshot: discount,
          version: latestVersion ? latestVersion.version + 1 : 1,
          discountId: discount.id
        }
      })
    })

        logInfo(`DiscountUpdateAction -> Discount with name ${name} updated successfully by ${userEmail}`)
      return{
          status: "SUCCESS",
          message: "Updated the discount with success."
      }
    }catch(e){
        logError(`DiscountUpdateAction -> Database error: ${e}  by ${userEmail}`)
      return {
        status: "ERROR",
        message: "Database error occurred while updating the discount. Please try again later.",
      };
    }
  }catch(e){
      logError(`DiscountUpdateAction -> Server error: ${e}  by ${userEmail}`)
    return{
      status: "ERROR",
      message: "Unexpected server error. Please try again later.",
    }
  }
}

export async function DiscountAddProductAction(data: DiscountAddProductToFormValues): Promise<ReturnActionWithFields> {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  try {
    const parsed = DiscountAddProductSchema.safeParse(data);

    if (!parsed.success) {
      const fieldErrors = Object.fromEntries(
        Object.entries(parsed.error.flatten().fieldErrors).map(([key, value]) => [key, value?.[0] || ""])
      );
      logError(`DiscountAddProductAction -> Error at parsing: ${parsed.error} by ${userEmail}`)
      return {
        status: "ERROR",
        message: "Validation error",
        fieldErrors
      };
    }

    const { discountId, materialNumber } = parsed.data;

    const discountExists = await prisma.discount.findUnique({
      where: { id: discountId },
    });

    if (!discountExists) {
      logError(`DiscountAddProductAction -> Discount not found: ${discountId} by ${userEmail}`)
      return {
        status: "ERROR",
        message: "Discount not found",
      };
    }

    const product = await prisma.product.findUnique({
      where: { Material_Number: materialNumber },
    });

    if (!product) {
      logError(`DiscountAddProductAction -> Product "${materialNumber}" not found by ${userEmail}`)
      return {
        status: "ERROR",
        message: "Product not found.",
      };
    }

    // Check if product already has a discount
    const existingDiscount = await prisma.productDiscount.findFirst({
      where: {
        productId: product.id,
      },
    });

    if (existingDiscount) {
      logError(`DiscountAddProductAction -> Product "${materialNumber}" already has a discount by ${userEmail}`)
      return {
        status: "ERROR",
        message: "Product already has a discount.",
      };
    }

    try {
      // Get the current version of the discount
      const currentVersion = await prisma.discountHistory.count({
        where: {
          discountId,
        },
      });

      const newDiscountVersion = currentVersion + 1;

      await prisma.$transaction(async (tx) => {
        // Create the product discount association
        await tx.productDiscount.create({
          data: {
            productId: product.id,
            discountId,
          },
        });

        const {id, createdAt, updatedAt, name, ...newDiscountExists} = discountExists

        await tx.discountHistory.create({
          data:{
            change_type:"PRODUCT_ADDED",
            changed_by: userEmail,
            changes:{insertedProduct: product.Material_Number},
            snapshot: newDiscountExists,
            version: newDiscountVersion,
            discountId
          }
        });
      });

      // After transaction completes, update the product price
      // Add debug logging
      logInfo(`DiscountAddProductAction -> Calling updateProductPrice for product ${product.id} (${materialNumber})`);
          
      // Call the function and log the result
      const updateResult = await updateProductPrice(product.id);
      logInfo(`DiscountAddProductAction -> updateProductPrice result: ${updateResult ? "Success" : "Failed"}`);
      
      revalidatePath(`/discounts/${discountId}`);
      logInfo(`DiscountAddProductAction -> Discount with id ${discountId} updated successfully by ${userEmail}`);
      
      return {
        status: "SUCCESS",
        message: "Updated the discount with success."
      };
    } catch(e) {
      logError(`DiscountAddProductAction -> Database error: ${e} by ${userEmail}`);
      return {
        status: "ERROR",
        message: "Database error occurred while adding the product. Please check if the product has an active discount",
      };
    }
  } catch(e) {
    logError(`DiscountAddProductAction -> Server error: ${e} by ${userEmail}`);
    return {
      status: "ERROR",
      message: "Unexpected server error. Please try again later.",
    };
  }
}


export async function DiscountDeleteProductAction(data: DiscountDeleteProductFormValues): Promise<ReturnActionWithFields>  {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  try {
    const parsed = deleteProductFromDiscountSchema.safeParse(data);

    if (!parsed.success) {
        logError(`DiscountDeleteProductAction -> Error at parsing: ${parsed.error} by ${userEmail}`)
      return {
        status: "ERROR",
        message: "Validation error",
      };
    }

    const { discountId, productId } = parsed.data;

    const discount = await prisma.discount.findUnique({
      where: { id: discountId },
      select: { name: true, value: true, active: true, createdBy: true, description: true,type:true, startDate: true, endDate: true  }
    });

    if (!discount) {
        logError(`DiscountDeleteProductAction -> DiscountId ${discountId} not found for productId ${productId} by ${userEmail}`)
      return {
        status: "ERROR",
        message: "Discount not found",
      };
    }

    // Fetch the product details including Material_Number for ProductHistory
    const product = await prisma.product.findUnique({
      where: { id: productId },
      //select: { Material_Number: true, Description_Local: true, Base_Unit_Of_Measur: true, Cross_Plant: true, Material_Group: true, Net_Weight: true, Parts_Class: true, PretAM: true } // Select relevant product fields for history
    });

    if (!product) {
        logError(`DiscountDeleteProductAction -> ProductId ${productId} not found by ${userEmail}`);
        return {
            status: "ERROR",
            message: "Product not found.",
        };
    }

    const association = await prisma.productDiscount.findFirst({
      where: {
        discountId,
        productId,
      },
    });

    if (!association) {
        logError(`DiscountDeleteProductAction -> ProductId ${productId} is not associated with discountId: ${discountId} by ${userEmail}`)
      return {
        status: "ERROR",
        message: "Product is not associated with this discount",
      };
    }

    try{
      await prisma.$transaction(async (tx) => {
        await tx.productDiscount.delete({
          where: {
            id: association.id,
          },
        });

        const lastDiscountVersion  = await tx.discountHistory.findFirst({
          where: { discountId  },
          orderBy: { version: "desc" },
          select: { version: true },
        });

        // Calculate the new version
       const newDiscountVersion = (lastDiscountVersion?.version ?? 0) + 1;

       const {name, ...discountSnapshot} = discount

        await tx.discountHistory.create({
          data:{
            change_type:"PRODUCT_DELETE",
            changed_by: userEmail,
            changes: { removedProduct: product.Material_Number }, 
            snapshot: discountSnapshot,
            version: newDiscountVersion,
            discountId
          }
        })

           // --- Product History Log (for the product itself) ---
        const lastProductVersion = await tx.productHistory.findFirst({
          where: { Material_Number: product.Material_Number }, // Use product's material number
          orderBy: { version: "desc" },
          select: { version: true },
        });

        const newProductVersion = (lastProductVersion?.version ?? 0) + 1;

        await tx.productHistory.create({
          data:{
            change_type: "DELETED_FROM_DISCOUNT",
            changed_by:userEmail,
            version: newProductVersion,
            Material_Number: product.Material_Number,
            changes: { discountName: discount.name },
            snapshot: product,
          }
        })
      })

      //After transaction completes, update the product price
      await updateProductPrice(product.id);

      revalidatePath(`/discounts/${discountId}`);

        logInfo(`DiscountDeleteProductAction -> deleted with success the productId ${productId} for the discountId ${discountId} by ${userEmail}`)
      return{
        status: "SUCCESS",
        message: "Deleted product with success."
      }
    }catch(e){
        logError(`DiscountDeleteProductAction -> Database error: ${e}  by ${userEmail}`)
      return {
        status: "ERROR",
        message: "Database error occurred while deleting the product.",
      };
    }

  }catch(e){
      logError(`DiscountDeleteProductAction -> Server error: ${e}  by ${userEmail}`)
    return{
      status: "ERROR",
      message: "Unexpected server error. Please try again later.",
    }
  }
}

export async function DiscountProcessCSVAction(data: DiscountProcessCSVFormValues): Promise<ReturnActionWithArray>{
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  try{

    const parsed = processCSVDiscountSchema.safeParse(data);

    if (!parsed.success) {
      console.error(parsed.error.flatten());
        logError(`DiscountProcessCSVAction -> Error at parsing: ${parsed.error}  by ${userEmail}`)
      return {
        status: "ERROR",
        message: "Invalid data structure / Some products dont have 11 characters",
      }
    }

    const { discountId, materialNumbers } = parsed.data;

    if (!materialNumbers || materialNumbers.length === 0) {
      logError(`DiscountProcessCSVAction -> No material numbers provided  by ${userEmail}`)
      return {
        status: "ERROR",
        message: "No material numbers provided.",
      };
    }

    // Fetch products based on the material numbers
    const products = await prisma.product.findMany({
      where: {
        Material_Number: {
          in: materialNumbers,
        },
      },
    });

    const foundMaterialNumbers = products.map((product) => product.Material_Number);

    // Identify missing products
    const missingMaterialNumbers = materialNumbers.filter(
      (mn) => !foundMaterialNumbers.includes(mn)
    );

    if (missingMaterialNumbers.length > 0) {
      logError(`DiscountProcessCSVAction -> Missing products: ${missingMaterialNumbers.join(", ")}  by ${userEmail}`)

      return {
        status: "ERROR",
        message: `The following material numbers were not found in the database: ${missingMaterialNumbers.join(", ")}`,
        invalidProducts: missingMaterialNumbers,
      };
    }

    if (products.length === 0) {
        logError(`DiscountProcessCSVAction -> No products found in DB for the provided material numbers  by ${userEmail}`)
      return {
        status: "ERROR",
        message: `No products found in DB for the provided material numbers: ${materialNumbers}`,
        invalidProducts: materialNumbers
      }
    }

    // Extract product IDs
    const productIds = products.map((product) => product.id);

    // Find products that are already associated with another discount
    const existingProductDiscounts = await prisma.productDiscount.findMany({
      where: {
        productId: {
          in: productIds,
        },
      },
      select: {
        productId: true,
        product: {
          select: { // Ensure Material_Number is selected here for `existingMaterialNumbers`
            Material_Number: true,
          },
        },
        discount: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    const existingMaterialNumbers = existingProductDiscounts.map(
      (pd) => pd.product.Material_Number
    );

    const newProductIdsToAssociate  = productIds.filter(
      (id) => !existingProductDiscounts.some((pd) => pd.productId === id)
    );

    // If there are existing products in other discounts, notify the user
    if (existingMaterialNumbers.length > 0) {
        logError(`DiscountProcessCSVAction -> Some products are already associated with a discount: ${existingMaterialNumbers}  by ${userEmail}`)
      return {
        status: "ERROR",
        message: `Some products are already associated with a discount: , ${existingMaterialNumbers}`,
        invalidProducts: existingMaterialNumbers
      }
    }

    // Get the discount details for the history snapshot
    const discount = await prisma.discount.findUnique({
      where: { id: discountId },
      select: { id: true, name: true, description: true, type: true, value: true, startDate: true, endDate: true, active: true, createdBy: true }
    });

    if (!discount) {
        logError(`DiscountProcessCSVAction -> Discount ${discountId} not found during processing by ${userEmail}`);
        return {
            status: "ERROR",
            message: "Discount not found during processing.",
        };
    }

    // Prepare data for ProductDiscount creation
    const productDiscountsToCreate = newProductIdsToAssociate.map((productId) => ({
      productId,
      discountId,
    }));


    try{
      if (productDiscountsToCreate.length > 0) {
          await prisma.$transaction(async (tx) => {
            // 1. Insert new ProductDiscount records
            await tx.productDiscount.createMany({
              data: productDiscountsToCreate,
            });

            // 2. Get the last version for DiscountHistory
            const lastVersionDiscount = await tx.discountHistory.findFirst({
              where: { discountId },
              orderBy: { version: "desc" },
              select: { version: true },
            });

            const newVersionDiscount = (lastVersionDiscount?.version ?? 0) + 1;

            // 3. Create DiscountHistory entry for the bulk operation
            const addedMaterialNumbers = products
              .filter(p => newProductIdsToAssociate.includes(p.id))
              .map(p => p.Material_Number);

            const {id, name, ...newDiscountExists} = discount

            await tx.discountHistory.create({
              data: {
                change_type: "PRODUCT_ADD_TO_DISCOUNT_WITH_CSV", // A specific type for this bulk action
                changes: {
                  addedProductsCount: addedMaterialNumbers.length,
                  addedMaterialNumbers: addedMaterialNumbers, // List the materials added
                },
                changed_by: userEmail,
                snapshot: newDiscountExists, // Snapshot the discount itself
                version: newVersionDiscount,
                discountId,
              },
            });

          // --- Product History Log (for the product itself) ---
          const productHistoryEntries = [];

          for (const addedProductId of newProductIdsToAssociate) {
            const productAdded = products.find(p => p.id === addedProductId);

            if (productAdded) {
              const lastProductVersion = await tx.productHistory.findFirst({
                where: { Material_Number: productAdded.Material_Number },
                orderBy: { version: "desc" },
                select: { version: true },
              });

              const newProductVersion = (lastProductVersion?.version ?? 0) + 1;

              // Ensure snapshot is of the product itself, excluding fields like `id` if `snapshot` is JSON
              //const { id: productIdFromProduct, ...productSnapshotWithoutId } = productAdded;

              productHistoryEntries.push({
                change_type: "ADDED_TO_DISCOUNT", // Specific type for product history
                changed_by: userEmail,
                version: newProductVersion,
                Material_Number: productAdded.Material_Number,
                changes: {
                  discountId: discount.id,
                  discountName: discount.name,
                },
                snapshot: productAdded, // Snapshot the product's details
              });


              await tx.productHistory.createMany({
                data: {
                  change_type: "PRODUCT_ADD_TO_DISCOUNT_WITH_CSV",
                  changed_by: userEmail,
                  Material_Number: productAdded.Material_Number,
                  version: newProductVersion,
                  changes: {
                    discountName: discount.name,
                    discountValue: discount.value
                  },
                  snapshot: productAdded
                }
              })

            }
          }
        }); // End of transaction
      }
          

      revalidatePath(`/discounts/products/${discountId}`)
        
         logInfo(`DiscountProcessCSVAction -> Success for discount ${discountId} with products: ${materialNumbers.join(", ")} by ${userEmail}`);
      return{
          status: "SUCCESS",
          message: "CSV file processed with success"
      }
    }catch(e){
        logError(`DiscountProcessCSVAction -> Database error: ${e}  by ${userEmail}`)
      return {
        status: "ERROR",
        message: "Database error occurred while adding the products.",
      };
    }

  }catch(e){
      logError(`DiscountProcessCSVAction -> Server error: ${e} by ${userEmail}`)
    return{
      status: "ERROR",
      message: "Unexpected server error. Please try again later.",
    }
  }
}

export async function DiscountDeleteAllProductsAction(data: DiscountDeleteAllProductsFormValues): Promise<ReturnAction>{
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  try {
    const parsed = DiscountDeleteAllProductsSchema.safeParse(data);

    if (!parsed.success) {
        logError(`DiscountDeleteAllProductsAction -> Error at parsing: ${parsed.error}  by ${userEmail}`)
      return {
        status: "ERROR",
        message: "Validation error",
      };
    }

    const { discountId } = parsed.data;

    const discount = await prisma.discount.findUnique({
      where: { id: discountId },
    });

    if (!discount) {
        logError(`DiscountDeleteAllProductsAction -> DiscountId ${discountId} not found by ${userEmail}`)
      return {
        status: "ERROR",
        message: "Discount not found",
      };
    }

    // --- NEW: Get products currently associated with this discount BEFORE deletion ---
    const associatedProducts = await prisma.productDiscount.findMany({
      where: { discountId },
      select: {
        productId: true,
        product: true
      },
    });

    const deletedMaterialNumbers = associatedProducts.map(ap => ap.product.Material_Number);

    try{
      let deletedProductCount = 0; // Initialize count to be updated inside transaction if needed

      await prisma.$transaction(async (tx) => {
        // 1. Delete all associated productDiscounts
        const deletedResult = await tx.productDiscount.deleteMany({
          where: {
            discountId,
          },
        });
        deletedProductCount = deletedResult.count;

        // Only create history if any products were actually deleted
        if (deletedProductCount > 0) {
          // --- Discount History Log ---
          const lastDiscountVersion = await tx.discountHistory.findFirst({
            where: { discountId },
            orderBy: { version: "desc" },
            select: { version: true },
          });

          const newDiscountVersion = (lastDiscountVersion?.version ?? 0) + 1;

          const { id, name, ...newDiscountSnapshot } = discount; // Prepare snapshot for discount

          await tx.discountHistory.create({
            data: {
              change_type: "PRODUCT_DELETE_ALL", // More specific type for this action
              changes: {
                deletedProductsCount: deletedProductCount,
                deletedMaterialNumbers: deletedMaterialNumbers, // Log the material numbers that were deleted
              },
              changed_by: userEmail,
              snapshot: newDiscountSnapshot, // Snapshot the discount itself
              version: newDiscountVersion,
              discountId,
            },
          });

          // --- Product History Log (for each deleted product) ---
          for (const associatedProduct of associatedProducts) {
            const productToDeleteHistory = associatedProduct.product; // Get the full product object

                const lastProductVersion = await tx.productHistory.findFirst({
                  where: { Material_Number: productToDeleteHistory.Material_Number },
                  orderBy: { version: "desc" },
                  select: { version: true },
                });

                const newProductVersion = (lastProductVersion?.version ?? 0) + 1;

                await tx.productHistory.create({
                  data: {
                    change_type:"PRODUCT_DELETE_ALL",
                    changed_by: userEmail,
                    version: newProductVersion,
                    Material_Number: productToDeleteHistory.Material_Number,
                    changes: { 
                      discountName: discount.name,
                      discountValue: discount.value
                    },
                    snapshot: productToDeleteHistory, // Snapshot the product's details
                  }
                })
          }
        }
      }); // End of transaction


        revalidatePath(`/discounts/${discountId}`);

        logInfo(`DiscountDeleteAllProductsAction -> deleted with success ${deletedProductCount} products from the discountId ${discountId}  by ${userEmail}`)
      return{
        status: "SUCCESS",
        message: `Deleted with success ${deletedProductCount} products.`
      }
    }catch(e){
        logError(`DiscountDeleteAllProductsAction -> Database error:${e} by ${userEmail}`)
      return {
        status: "ERROR",
        message: "Database error occurred while deleting the product.",
      };
    }

  }catch(e){
      logError(`DiscountDeleteAllProductsAction -> Server error: ${e} by ${userEmail}`)
    return{
      status: "ERROR",
      message: "Unexpected server error. Please try again later.",
    }
  }
}

export async function ProductAddAttributeAction(data: ProductAddAttributeFormValues):Promise<ReturnAction>{
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  const parsed = ProductAddAttributeSchema.safeParse(data);

  if (!parsed.success) {
        logError(`ProductAddAttributeAction -> Error at parsing: ${parsed.error} by ${userEmail}`)
    return {
      status: "ERROR",
      message: "Validation error",
    };
  }
  
  const { materialNumber, key, value } = parsed.data;

  try {

    // Check if the product exists (optional but good practice)
    const productExists = await prisma.product.findUnique({
      where: { Material_Number: materialNumber },
      select: { id: true, Material_Number: true } // Select necessary fields
    });

    if (!productExists) {
          logError(`ProductAddAttributeAction -> Product with Material Number ${materialNumber} not found by ${userEmail}`)
      return { status: "ERROR", message: `ProductAddAttributeAction -> Product with Material Number ${materialNumber} not found`};
    }

    // Check for unique constraint violation (Material_Number_key)
    const existingAttribute = await prisma.productAttribute.findUnique({
        where: {
            Material_Number_key: {
                Material_Number: materialNumber,
                key: key,
            }
        }
    });

    if (existingAttribute) {
          logError(`ProductAddAttributeAction -> Attribute with key "${key}" already exists for this product by ${userEmail}`)
         return { status: "ERROR", message: `ProductAddAttributeAction -> Attribute with key "${key}" already exists for this product.` };
    }

    await prisma.$transaction(async (tx) => {
      // 1. Create the new product attribute
      const newAttribute = await tx.productAttribute.create({
        data: {
          Material_Number: materialNumber,
          key: key,
          value: value,
        },
      });

      // 2. Find the last version for productAttributeHistory for this Material_Number
      const lastVersion = await tx.productAttributeHistory.findFirst({
        where: {
          Material_Number: materialNumber
        },
        orderBy: {
          version: "desc"
        },
        select: {
          version: true
        }
      });

      // Calculate the new version
      const newVersion = (lastVersion?.version ?? 0) + 1; // Correct version calculation

      // 3. Create the product attribute history entry
      await tx.productAttributeHistory.create({
        data: {
          Material_Number: materialNumber,
          change_type: "INSERT", // Correct for initial creation of the attribute
          changed_by: userEmail,
          changes: { [key]: value }, // Log what was added
          snapshot: newAttribute, // Snapshot the newly created attribute
          version: newVersion
        }
      });
    }); // End of transaction


    revalidatePath(`/product/attributes/${materialNumber}`); // Revalidate the list

      logInfo(`ProductAddAttributeAction -> Attribute "${key}" added successfully for product ${materialNumber} by ${userEmail}`);
    return { status: "SUCCESS", message: 'Attribute added successfully!' };
  }catch(e){
      logError(`ProductAddAttributeAction -> Server error: ${e}  by ${userEmail}`)
    return{
      status: "ERROR",
      message: "Unexpected server error. Please try again later.",
    }
  }
}

export async function ProductDeleteAttributeAction(data: ProductDeleteAttributeSchemaFormValues):Promise<ReturnAction>{
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  const parsed = ProductDeleteAttributeSchema.safeParse(data);

  if (!parsed.success) {
        logError(`DeleteProductAttributeAction -> Error at parsing: ${parsed.error} by ${userEmail}`)
    return {
      status: "ERROR",
      message: "Validation error",
    };
  }
  
  const { materialNumber, attributeKey } = data;
  console.log(materialNumber, attributeKey)
  try {
    const productExists = await prisma.product.findUnique({
      where: { Material_Number: materialNumber },
    });

    if (!productExists) {
        logError(`DeleteProductAttributeAction -> Product with Material Number ${materialNumber} not found by ${userEmail}`)
      return { 
        status: "ERROR", 
        message: `Product with Material Number ${materialNumber} not found `};
    }

    const keyExist = await prisma.productAttribute.findUnique({
      where: {
            Material_Number_key: {
            Material_Number: materialNumber,
            key: attributeKey,
          },
      }
    })

    if(!keyExist){
        logError(`DeleteProductAttributeAction -> Key ${attributeKey} for Material Number ${materialNumber} not found by ${userEmail}`)
      return { 
        status: "ERROR", 
        message: `Key ${attributeKey} for Material Number ${materialNumber} not found `};
    }

        const version = await prisma.productAttributeHistory.findFirst({
          select: {
            version: true
          },
          where: {
            Material_Number: materialNumber
          },
          orderBy: {
            version : "desc"
          }
        })

    try{
      await prisma.productAttribute.delete({
        where: {
          Material_Number_key: {
            Material_Number: materialNumber,
            key: attributeKey,
          },
        },
      });

      await prisma.productAttributeHistory.create({
        data: {
          Material_Number: materialNumber,
          change_type: "DELETE",
          changed_by: userEmail,
          changes: `${keyExist.key}:${keyExist.value}`,
          snapshot: keyExist,
          version: version ? version.version + 1 : 1
        }
      })

      revalidatePath(`/product/attributes/${materialNumber}`);

        logInfo(`DeleteProductAttributeAction -> deleted with success the key ${attributeKey} for the product ${materialNumber} by ${userEmail}`)
      return{
        status: "SUCCESS",
        message: "Deleted key with success."
      }
    }catch(e){
        logError(`DeleteProductAttributeAction -> Database error: ${e} for ${attributeKey} for the product ${materialNumber} by ${userEmail}`)
      return {
        status: "ERROR",
        message: "Database error occurred while deleting the product.",
      };
    }

  }catch(e){
      logError(`DeleteProductAttributeAction -> Server error: ${e} for ${attributeKey} for the product ${materialNumber}  by ${userEmail}`)
    return{
      status: "ERROR",
      message: "Unexpected server error. Please try again later.",
    }
  }
}

export async function ProductAddAttributeCSVAction(data: ProductAddAttributeFormValues[]): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  const parsed = ProductAddAttributeArraySchema.safeParse(data);

  if (!parsed.success) {
    logError(`ProductAddAttributeCSVAction -> Error at parsing: ${parsed.error.message} by ${userEmail}`);
    return {
      status: "ERROR",
      message: "Error at parsing/validation the file.",
    };
  }

  const csvItems = parsed.data;

  if (csvItems.length === 0) {
    logError(`ProductAddAttributeCSVAction -> No data found in the file by ${userEmail}`);
    return {
      status: "ERROR",
      message: "No data found in the file. Nothing processed.",
    };
  }

  const productsToCreateAttributesFor: ProductAddAttributeFormValues[] = [];
  const invalidItems: { materialNumber: string; key: string; reason: string }[] = [];

  // --- Step 1: Pre-validate and filter items locally in memory ---
  // This avoids hitting the DB in a loop for simple checks
  const materialNumbersInCSV = [...new Set(csvItems.map(item => item.materialNumber))];

  // Fetch all products involved in the CSV in one go
  const existingProducts = await prisma.product.findMany({
    where: {
      Material_Number: {
        in: materialNumbersInCSV,
      },
    },
  });
  const existingProductMap = new Map(existingProducts.map(p => [p.Material_Number, p]));

  // Fetch all existing attributes for these products in one go
  const existingAttributes = await prisma.productAttribute.findMany({
    where: {
      Material_Number: {
        in: materialNumbersInCSV,
      },
    },
    select: { Material_Number: true, key: true },
  });
  // Create a Set for efficient lookup of existing Material_Number-key combinations
  const existingAttributeCombinations = new Set(existingAttributes.map(attr => `${attr.Material_Number}|${attr.key}`));

  // Iterate through CSV items to identify valid ones and errors
  for (const item of csvItems) {
    const { materialNumber, key, value } = item;

    if (!existingProductMap.has(materialNumber)) {
      invalidItems.push({ materialNumber, key, reason: `Product with Material Number "${materialNumber}" not found.` });
      continue;
    }

    if (existingAttributeCombinations.has(`${materialNumber}|${key}`)) {
      invalidItems.push({ materialNumber, key, reason: `Attribute with key "${key}" already exists for product "${materialNumber}".` });
      continue;
    }

    // Add to a list for batch creation
    productsToCreateAttributesFor.push(item);
  }

  // If no valid attributes to add, return early
  if (productsToCreateAttributesFor.length === 0) {
    const message = invalidItems.length > 0
                                        ? `No attributes were added. Found ${invalidItems.length} invalid items: ${invalidItems.map(i=>i.materialNumber)}.`
                                        : "No valid attributes found to add.";
    logInfo(`ProductAddAttributeCSVAction -> ${message} by ${userEmail}`);
    return {
      status: "ERROR", 
      message
    };
  }

  let processedCount = 0;

  try {
    await prisma.$transaction(async (tx) => {
      const productAttributeData: any[] = [];

      for (const item of productsToCreateAttributesFor) {
        const { materialNumber, key, value } = item;

        // 1. Prepare data for ProductAttribute creation (will use createMany)
        productAttributeData.push({
          Material_Number: materialNumber,
          key: key,
          value: value,
        });
      }

      // 2. Create all new product attributes in one go
      await tx.productAttribute.createMany({
        data: productAttributeData,
      });

      const newlyCreatedAttributes = await tx.productAttribute.findMany({
        where: {
          OR: productsToCreateAttributesFor.map(item => ({
            Material_Number: item.materialNumber,
            key: item.key
          }))
        },
        // Select all fields relevant for a snapshot, including `id` if your snapshot field is JSON
        select: { id: true, Material_Number: true, key: true, value: true, createdAt: true, updatedAt: true }
      });

      // Create a map for quick lookup: "Material_Number|key" -> newly created attribute object
      const newlyCreatedAttributeMap = new Map(
          newlyCreatedAttributes.map(attr => [`${attr.Material_Number}|${attr.key}`, attr])
      );


      for (const item of productsToCreateAttributesFor) {
        const { materialNumber, key, value } = item;
        const product = existingProductMap.get(materialNumber); // Already fetched

        if (!product) {
          // This case should ideally not be hit due to earlier checks
          logError(`ProductAddAttributeCSVAction -> Internal error: Product ${materialNumber} not found for history creation.`);
          return{
            status: "ERROR",
            message: `Internal error: Product ${materialNumber} not found for history creation`,
          }
        }

        const attributeSnapshot = newlyCreatedAttributeMap.get(`${materialNumber}|${key}`);

        if (!attributeSnapshot) {
             logError(`ProductAddAttributeCSVAction -> Internal error: Created attribute snapshot for ${materialNumber}:${key} not found.`);
          return{
            status: "ERROR",
            message: `Internal error: Created attribute snapshot for ${materialNumber}:${key} not found.`,
          }
        }

        // Get the last version for productAttributeHistory for this Material_Number
        const lastProductAttributeHistoryVersion = await tx.productAttributeHistory.findFirst({
          where: { Material_Number: materialNumber },
          orderBy: { version: "desc" },
          select: { version: true },
        });

        const newVersion = (lastProductAttributeHistoryVersion?.version ?? 0) + 1;

        await tx.productAttributeHistory.create({
          data: {
            Material_Number: materialNumber,
            change_type: "INSERT", // Correct for initial creation of the attribute
            changed_by: userEmail,
            changes: { [key]: value }, // Log what was added
            snapshot: attributeSnapshot, // Snapshot the newly created attribute
            version: newVersion
          }
        });

        processedCount++; // Increment count only for successfully processed items
      }

    }); // End of transaction

    revalidatePath(`/product/attributes`); // Revalidate a general path for attributes

    logInfo(`ProductAddAttributeCSVAction -> Successfully added ${processedCount} attributes from CSV by ${userEmail}`);
    return {
      status: "SUCCESS",
      message: `Successfully added ${processedCount} attributes! ${invalidItems.length > 0 ? `(${invalidItems.length} items skipped due to errors)` : ''}`,
    };
  } catch (e) {
    logError(`ProductAddAttributeCSVAction -> Database error during transaction: ${e} by ${userEmail}`);
    return {
      status: "ERROR",
      message: `Database error occurred while adding attributes. Please try again later for: ${invalidItems.map(i=>i.materialNumber)}.`,
    };
  }
}

export async function handleExpiredDiscounts():Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  const now = new Date();
  logInfo(`Starting expired discount check at ${now.toISOString()} by ${userEmail}`);
  
  try {
    // Find all active discounts that have expired
    const expiredDiscounts = await prisma.discount.findMany({
      where: {
        active: true,
        endDate: { lt: now },
      },
      include: {
        productDiscounts: {
          select: {
            id: true,
            productId: true,
          },
        },
      },
    });

    logInfo(`Found ${expiredDiscounts.length} expired discounts to process by ${userEmail}`);
    
    // Process each expired discount
    for (const discount of expiredDiscounts) {
      try {
        await prisma.$transaction(async (tx) => {
          // 1. Delete all product associations
          await tx.productDiscount.deleteMany({
            where: { discountId: discount.id },
          });
          
          // 2. Update the discount to inactive
          await tx.discount.update({
            where: { id: discount.id },
            data: { active: false },
          });
          
          // 3. Create history entry for this automated action
          const { id, name, ...discountSnapshot } = discount;
          await tx.discountHistory.create({
            data: {
              change_type: "MANUAL_DISCOUNT_EXPIRED_BUTTON_PRESS",
              changed_by:   userEmail,
              changes: { 
                expiredAt: now.toISOString(),
                productsRemoved: discount.productDiscounts.length
              },
              snapshot: discountSnapshot,
              version: await getNextDiscountVersion(tx, discount.id),
              discountId: discount.id
            }
          });
        });
        
        // Update prices for all affected products
        for (const pd of discount.productDiscounts) {
          await updateProductPrice(pd.productId);
        }
        
        // Revalidate the discounts page to show updated data
        revalidatePath('/discounts');
        logInfo(`Processed expired discount ${discount.id}: deactivated and removed ${discount.productDiscounts.length} products by ${userEmail}`);
      } catch (error) {
        logError(`Error processing expired  by ${userEmail} for discount ${discount.id}: ${error}`);
        // Continue with other discounts even if one fails
      }
    }
    return {
      status: "SUCCESS",
      message: `Processed expired discounts ${expiredDiscounts.length}.`,
    }
  } catch (error) {
    logError(`Error in handleExpiredDiscounts: ${error} by ${userEmail}`);
    return {
      status: "ERROR",
      message: "Failed to process expired discounts.",
    };
  }
}

export async function handleActivateDiscounts():Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  const now = new Date();
  
  logInfo(`Starting activate discount check at ${now.toISOString()} by ${userEmail}`);

  try {
    // Find all inactive discounts that should be activated (start date has arrived)
    const discountsToActivate = await prisma.discount.findMany({
      where: {
        active: false,
        startDate: { lte: now },
        endDate: { gt: now }, // Make sure they haven't expired yet
      },
      include: {
        productDiscounts: {
          select: {
            id: true,
            productId: true,
          },
        },
      },
    });

    logInfo(`Found ${discountsToActivate.length} discounts to activate by ${userEmail}`);

    // Process each discount that should be activated
    for (const discount of discountsToActivate) {
      try {
        await prisma.$transaction(async (tx) => {
          // 1. Update the discount to active
          await tx.discount.update({
            where: { id: discount.id },
            data: { active: true },
          });

          // 2. Create history entry for this activation
          const { id, name, ...discountSnapshot } = discount;
          await tx.discountHistory.create({
            data: {
              change_type: "ADDED_TO_DISCOUNT",
              changed_by: userEmail,
              changes: {
                activatedAt: now.toISOString(),
                productsAffected: discount.productDiscounts.length
              },
              snapshot: { ...discountSnapshot, active: true },
              version: await getNextDiscountVersion(tx, discount.id),
              discountId: discount.id
            }
          });
        });

        // Update prices for all affected products
        for (const pd of discount.productDiscounts) {
          await updateProductPrice(pd.productId);
        }

        logInfo(`Activated discount ${discount.id}: activated and updated ${discount.productDiscounts.length} products`);
      } catch (error) {
        logError(`Error activating discount ${discount.id}: ${error}`);
        // Continue with other discounts even if one fails
      }
    }

    // Revalidate the discounts page to show updated data
    revalidatePath('/discounts');

    if (discountsToActivate.length === 0) {
      logInfo(`No discounts found that need activation by ${userEmail}`);
    }
    return {
      status: "SUCCESS",
      message: `Activated ${discountsToActivate.length} discounts.`,
    }

  } catch (error) {
    logError(`Error in handleActivateDiscounts: ${error} by ${userEmail}`);
    return {
      status: "ERROR",
      message: "Failed to activate discounts.",
    };
  }
}

// Helper function to get the next version number for discount history
async function getNextDiscountVersion(tx: any, discountId: string): Promise<number> {
  const lastVersion = await tx.discountHistory.findFirst({
    where: { discountId },
    orderBy: { version: "desc" },
    select: { version: true },
  });

  return (lastVersion?.version ?? 0) + 1;
}


