"use client";

import { useState, useTransition } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { ReturnStatus } from "@prisma/client";
import { updateReturnStatus } from "@/app/actions/returnActions";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface ReturnStatusUpdateProps {
  returnId: string;
  currentStatus: ReturnStatus;
}

export default function ReturnStatusUpdate({ 
  returnId, 
  currentStatus 
}: ReturnStatusUpdateProps) {
  const [isPending, startTransition] = useTransition();
  const [status, setStatus] = useState<ReturnStatus>(currentStatus);
  const [notes, setNotes] = useState("");
  const router = useRouter();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    startTransition(async () => {
      try {
        const response = await updateReturnStatus(returnId, status, notes);
        
        if (response.status === "SUCCESS") {
          toast.success(response.message);
          router.refresh();
        } else {
          toast.error(response.message);
        }
      } catch (error) {
        console.error("Error updating return status:", error);
        toast.error("An unexpected error occurred");
      }
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Update Return Status</CardTitle>
        <CardDescription>
          Change the status of this return request
        </CardDescription>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Status</label>
            <Select
              value={status}
              onValueChange={(value) => setStatus(value as ReturnStatus)}
              disabled={isPending}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                {Object.values(ReturnStatus).map((statusOption) => (
                  <SelectItem key={statusOption} value={statusOption}>
                    {statusOption.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Notes</label>
            <Textarea
              placeholder="Add notes about this status change"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              disabled={isPending}
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-end space-x-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              setStatus(currentStatus);
              setNotes("");
            }}
            disabled={isPending}
          >
            Reset
          </Button>
          <Button 
            type="submit" 
            disabled={isPending || status === currentStatus}
          >
            {isPending ? "Updating..." : "Update Status"}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}