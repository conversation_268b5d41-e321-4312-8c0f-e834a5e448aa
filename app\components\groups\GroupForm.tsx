"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";
import { UserGroup } from "@/generated/prisma";

const groupSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name too long"),
  description: z.string().max(500, "Description too long").optional(),
  permissions: z.array(z.string()).min(1, "At least one permission is required"),
});

type GroupFormValues = z.infer<typeof groupSchema>;

interface GroupFormProps {
  group?: UserGroup;
  onSubmit: (data: GroupFormValues) => Promise<void>;
}

const availablePermissions = [
  "users.read",
  "users.write",
  "users.delete",
  "groups.read",
  "groups.write",
  "groups.delete",
  "banners.read",
  "banners.write",
  "banners.delete",
  "discounts.read",
  "discounts.write",
  "discounts.delete",
  "reports.read",
  "reports.write",
  "audit.read",
];

export default function GroupForm({ group, onSubmit }: GroupFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newPermission, setNewPermission] = useState("");

  const form = useForm<GroupFormValues>({
    resolver: zodResolver(groupSchema),
    defaultValues: {
      name: group?.name || "",
      description: group?.description || "",
      permissions: group?.permissions || [],
    },
  });

  const watchedPermissions = form.watch("permissions");

  const handleSubmit = async (values: GroupFormValues) => {
    setIsSubmitting(true);
    try {
      await onSubmit(values);
      toast.success(group ? "Group updated successfully" : "Group created successfully");
      router.push("/groups");
    } catch (error) {
      toast.error("Failed to save group");
    } finally {
      setIsSubmitting(false);
    }
  };

  const addPermission = (permission: string) => {
    const currentPermissions = form.getValues("permissions");
    if (!currentPermissions.includes(permission)) {
      form.setValue("permissions", [...currentPermissions, permission]);
    }
  };

  const removePermission = (permission: string) => {
    const currentPermissions = form.getValues("permissions");
    form.setValue("permissions", currentPermissions.filter(p => p !== permission));
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Group Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter group name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="Enter group description" 
                  {...field} 
                />
              </FormControl>
              <FormDescription>
                Optional description of what this group is for
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="permissions"
          render={() => (
            <FormItem>
              <FormLabel>Permissions</FormLabel>
              <FormDescription>
                Select permissions for this group
              </FormDescription>
              
              <div className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  {availablePermissions
                    .filter(p => !watchedPermissions.includes(p))
                    .map((permission) => (
                    <Button
                      key={permission}
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => addPermission(permission)}
                    >
                      {permission}
                    </Button>
                  ))}
                </div>

                {watchedPermissions.length > 0 && (
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Selected permissions:</p>
                    <div className="flex flex-wrap gap-2">
                      {watchedPermissions.map((permission) => (
                        <Badge key={permission} variant="default" className="pr-1">
                          {permission}
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="h-auto p-1 ml-1"
                            onClick={() => removePermission(permission)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex gap-4">
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Saving..." : group ? "Update Group" : "Create Group"}
          </Button>
          <Button 
            type="button" 
            variant="outline" 
            onClick={() => router.push("/groups")}
          >
            Cancel
          </Button>
        </div>
      </form>
    </Form>
  );
}