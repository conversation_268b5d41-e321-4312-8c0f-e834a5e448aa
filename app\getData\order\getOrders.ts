
import prisma from "@/app/utils/db";
import { Order, OrderStatus } from "@/generated/prisma";
import { getCurrentUser } from "../user/data";
import { logError } from "@/lib/logger";
import { z } from "zod";

export async function getOrders(options?: {
  limit?: number;
  offset?: number;
  status?: OrderStatus;
  userId?: string;
}): Promise<Order[]> {

  const user = await getCurrentUser()
  const userEmail = user?.email

  try{
  //validate parameters with zod and create the schema
  const getOrdersSchema = z.object({
    limit: z.number().min(1).max(100).optional(),
    offset: z.number().min(0).optional(),
    status: z.enum(Object.values(OrderStatus) as [string, ...string[]]).optional(),
    userId: z.string().cuid().optional(),
  });
  
  const validatedOptions = getOrdersSchema.safeParse(options);
  if (!validatedOptions.success) {  
    logError(`getOrders -> Error at parsing: ${validatedOptions.error} by ${userEmail}`)
    return []
  }

  const { limit = 50, offset = 0, status, userId } = options || {};

  const where = {
    isActive: true,
    ...(status && { orderStatus: status }),
    ...(userId && { userId }),
  };

  const orders = await prisma.order.findMany({
    where,
    orderBy: {
      createdAt: "desc",
    },
    include: {
      user: {
        select: {
          firstName: true,
          lastName: true,
          email: true,
        },
      },
      billingAddress: true,
      shippingAddress: true,
    },
    take: limit,
    skip: offset,
  });

  if (!orders) {
    logError(`getOrders -> No orders found for ${userEmail}`)
    return [];
  }

  return orders;
  }catch(e){
    logError(`getOrders -> Unexpected error: ${e} for ${userEmail}`)
    return []
  }
}

export async function getOrderById(id: string) {
  const user = await getCurrentUser()
  const userEmail = user?.email
  try{
    //validate parameter with zod
    const validatedId = z.string().cuid().safeParse(id);
    if (!validatedId.success) {
      logError(`getOrderById -> Error at parsing: ${validatedId.error} by ${userEmail}`)
      return null;
    }
    id = validatedId.data


    const order = await prisma.order.findUnique({
      where: {
        id,
        isActive: true,
      },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        billingAddress: true,
        shippingAddress: true,
        orderItems: {
          include: {
            product: true,
          },
        },
        statusHistory: {
          orderBy: {
            createdAt: "desc",
          },
        },
      },
    });
   
    if (!order) {
      logError(`getOrderById -> Order not found with ID ${id} by ${userEmail}`)
      return null;
    }
    return order;
  }
  catch(e){
    logError(`getOrderById -> Unexpected error: ${e} for ${userEmail}`)
    return null
  }
}
