import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { formatCurrency } from "@/app/utils/formatters";
import Link from "next/link";

export default function OrderItemsTable({ items }) {
  if (!items || items.length === 0) {
    return <p className="text-muted-foreground">No items in this order</p>;
  }

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Product</TableHead>
            <TableHead>SKU</TableHead>
            <TableHead className="text-right">Price</TableHead>
            <TableHead className="text-right">Quantity</TableHead>
            <TableHead className="text-right">Total</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {items.map((item) => (
            <TableRow key={item.id}>
              <TableCell>
                <Link 
                  href={`/product/${item.product?.Material_Number || ''}`}
                  className="font-medium hover:underline"
                >
                  {item.product?.name || 'Unknown Product'}
                </Link>
              </TableCell>
              <TableCell>{item.product?.Material_Number || 'N/A'}</TableCell>
              <TableCell className="text-right">{formatCurrency(item.unitPrice)}</TableCell>
              <TableCell className="text-right">{item.quantity}</TableCell>
              <TableCell className="text-right">{formatCurrency(item.unitPrice * item.quantity)}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}