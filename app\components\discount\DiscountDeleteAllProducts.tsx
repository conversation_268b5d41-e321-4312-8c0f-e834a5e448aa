"use client"

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTransition } from "react";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { DiscountDeleteAllProductsFormValues, DiscountDeleteAllProductsSchema } from "@/app/zod/zod";
import { DiscountDeleteAllProductsAction } from "@/app/actions/actions";

interface DeleteAllProductsProps {
  discountId: string;
}

export default function DiscountDeleteAllProducts({ discountId }: DeleteAllProductsProps){
      const [isLoading, startTransition] = useTransition()
    
      const {
        //register,
        handleSubmit,
        //formState: { errors, isSubmitting },
        reset,
      } = useForm<DiscountDeleteAllProductsFormValues>({
        resolver: zodResolver(DiscountDeleteAllProductsSchema),
        defaultValues: { discountId },
      });
    
      const onSubmit = async (data: DiscountDeleteAllProductsFormValues) => {
  
        startTransition(async () => {
          try {
            const response = await DiscountDeleteAllProductsAction(data);
            if(response.status === "SUCCESS"){
              reset();
              toast.success(response.message);
            }else{
              toast.error(response.message)
            }  
          } catch (error) {
              console.error("Unexpected error:", error);
          }
        })
      };

    return (
        <>
            <form onSubmit={handleSubmit(onSubmit)} className="">
                <AlertDialog>
                    <AlertDialogTrigger asChild>
                        <Button variant="destructive">Delete</Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                            <AlertDialogDescription>
                                This action cannot be undone. This will permanently delete the product from the discount.
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction 
                                type="submit"
                                className="bg-red-500"
                                onClick={() => onSubmit({discountId})}
                                disabled={isLoading}
                                >{isLoading ? "Deleting..." : "Delete"}
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>
            </form>
        </>
  );
}