{"level":"error","message":"ProductSearchAction -> Product \"asdadasdsss\" not found by cosmin.oprea"}
{"level":"error","message":"ProductSearchAction -> Product \"asdadasdsss\" not found by cosmin.oprea","timestamp":"2025-05-23 12:54:31"}
{"level":"error","message":"ProductSearchAction -> Product \"asdadasdsss\" not found by cosmin.oprea","timestamp":"2025-05-23 12:55:02"}
{"level":"error","message":"DiscountUpdateAction -> The discount allready exists: test22  by cosmin.oprea","timestamp":"2025-05-23 14:10:58"}
{"level":"error","message":"DiscountAddProductAction -> Product \"asdasdasdsd\" not found by cosmin.oprea","timestamp":"2025-05-23 14:40:07"}
{"level":"error","message":"DiscountProcessCSVAction -> Missing products: 64115A1BDB6  by cosmin.oprea","timestamp":"2025-05-23 14:40:36"}
{"level":"error","message":"DiscountProcessCSVAction -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"string\",\n    \"received\": \"undefined\",\n    \"path\": [\n      \"discountId\"\n    ],\n    \"message\": \"Required\"\n  }\n]  by cosmin.oprea","timestamp":"2025-05-23 14:57:17"}
