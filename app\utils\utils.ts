import { DiscountType } from "@/generated/prisma";
import { Decimal } from "@prisma/client/runtime/library";
import prisma from "./db";
import { logError, logInfo } from "@/lib/logger";



/**
 * Calculates the final price based on a discount
 */
export function calculateFinalPrice(
  basePrice: Decimal | number,
  discountType: DiscountType | null,
  discountValue: Decimal | number | null
): Decimal {
  // Convert to Decimal if needed
  const basePriceDecimal = basePrice instanceof Decimal ? basePrice : new Decimal(basePrice);
  
  // If no discount, return base price
  if (!discountType || !discountValue) {
    return basePriceDecimal;
  }
  
  const discountValueDecimal = discountValue instanceof Decimal 
    ? discountValue 
    : new Decimal(discountValue);
  
  let finalPrice = basePriceDecimal;

  switch (discountType) {
    case "PERCENTAGE":
      // Ensure percentage is within valid range (0-100)
      const percentage = discountValueDecimal.toNumber();
      if (percentage > 0 && percentage <= 100) {
        finalPrice = basePriceDecimal.minus(
          basePriceDecimal.mul(percentage).div(100)
        );
        logInfo(`PERCENTAGE: ${basePriceDecimal} - ${percentage}% = ${finalPrice}`);
      }
      break;

    case "FIXED_AMOUNT":
      // Ensure fixed amount doesn't make price negative
      if (discountValueDecimal.greaterThan(0)) {
        finalPrice = Decimal.max(
          basePriceDecimal.minus(discountValueDecimal),
          new Decimal(0)
        );
        logInfo(`FIXED_AMOUNT: ${basePriceDecimal} - ${discountValueDecimal} = ${finalPrice}`);
      }
      break;

    case "NEW_PRICE":
      // New price becomes final price if lower than base price
      if (discountValueDecimal.greaterThan(0) && discountValueDecimal.lessThan(basePriceDecimal)) {
        finalPrice = discountValueDecimal;
        logInfo(`NEW_PRICE: New price set to ${finalPrice}`);
      }
      break;
  }

  return finalPrice;
}

/**
 * Calculates the discount percentage
 */
export function calculateDiscountPercentage(
  basePrice: Decimal | number,
  finalPrice: Decimal | number
): Decimal {
  // Convert to Decimal if needed
  const basePriceDecimal = basePrice instanceof Decimal ? basePrice : new Decimal(basePrice);
  const finalPriceDecimal = finalPrice instanceof Decimal ? finalPrice : new Decimal(finalPrice);
  
  // If final price is higher or equal to base price, no discount
  if (finalPriceDecimal.greaterThanOrEqualTo(basePriceDecimal) || basePriceDecimal.equals(0)) {
    return new Decimal(0);
  }
  
  // Calculate percentage off
  const discount = basePriceDecimal.minus(finalPriceDecimal);
  const percentage = discount.div(basePriceDecimal).mul(100);
  
  return percentage;
}

/**
 * Updates a product's price based on its active discount
 */
export async function updateProductPrice(productId: string): Promise<boolean> {
  try {
    logInfo(`Starting updateProductPrice for product ${productId}`);
    
    // Get the product
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: {
        id: true,
        PretAM: true,
        FinalPrice: true,
        Material_Number: true,
      },
    });

    if (!product || !product.PretAM) {
      logError(`updateProductPrice: Product ${productId} not found or has no base price`);
      return false;
    }

    logInfo(`Found product ${product.Material_Number} with base price ${product.PretAM}`);

    // Find active discount for this product
    const productDiscount = await prisma.productDiscount.findFirst({
      where: {
        productId,
        discount: {
          active: true,
          startDate: { lte: new Date() },
          endDate: { gte: new Date() },
        },
      },
      include: {
        discount: {
          select: {
            id: true,
            name: true,
            type: true,
            value: true,
          },
        },
      },
    });

    // Calculate new price
    let finalPrice: Decimal;
    let hasDiscount = false;
    let discountType = null;
    let discountValue = null;
    let discountPercentage = new Decimal(0);

    if (productDiscount) {
      // Apply discount
      discountType = productDiscount.discount.type;
      discountValue = productDiscount.discount.value;
      
      logInfo(`Found active discount for ${product.Material_Number}: ${discountType} with value ${discountValue}`);
      
      finalPrice = calculateFinalPrice(
        product.PretAM,
        discountType,
        discountValue
      );
      
      // Only mark as discounted if final price is actually lower
      if (finalPrice.lessThan(product.PretAM)) {
        hasDiscount = true;
        discountPercentage = calculateDiscountPercentage(product.PretAM, finalPrice);
        logInfo(`Discount applied: ${product.Material_Number} - Original: ${product.PretAM}, Final: ${finalPrice}, Discount %: ${discountPercentage}`);
      } else {
        // If discount doesn't actually reduce price, don't apply it
        finalPrice = product.PretAM;
        discountType = null;
        discountValue = null;
        logInfo(`Discount not applied: ${product.Material_Number} - Final price would not be lower`);
      }
    } else {
      // No discount
      finalPrice = product.PretAM;
      logInfo(`No active discount found for product ${product.Material_Number}`);
    }

    // Calculate price range
    let priceRange: string;
    const finalPriceNum = finalPrice.toNumber();
    
    if (finalPriceNum < 25) {
      priceRange = '0-25';
    } else if (finalPriceNum < 50) {
      priceRange = '25-50';
    } else if (finalPriceNum < 100) {
      priceRange = '50-100';
    } else if (finalPriceNum < 200) {
      priceRange = '100-200';
    } else if (finalPriceNum < 500) {
      priceRange = '200-500';
    } else if (finalPriceNum < 1000) {
      priceRange = '500-1000';
    } else if (finalPriceNum < 2000) {
      priceRange = '1000-2000';
    } else {
      priceRange = '2000+';
    }

    // Check if price actually changed
    const priceChanged = !product.FinalPrice || !finalPrice.equals(product.FinalPrice);
    
    if (priceChanged) {
      logInfo(`Price changed for ${product.Material_Number}: ${product.FinalPrice || 'none'} -> ${finalPrice}`);
    } else {
      logInfo(`No price change for ${product.Material_Number}: ${finalPrice}`);
    }

    // Update product with new price
    await prisma.product.update({
      where: { id: productId },
      data: {
        FinalPrice: finalPrice,
        HasDiscount: hasDiscount,
        activeDiscountType: discountType,
        activeDiscountValue: discountValue,
        discountPercentage: discountPercentage,
        priceRange: priceRange,
      },
    });

    logInfo(`Updated product ${product.Material_Number} with new price ${finalPrice}`);

    // Log price change if needed
    if (priceChanged) {
      await prisma.priceHistory.create({
        data: {
          productId: product.id,
          oldPretAM: product.PretAM,
          newPretAM: product.PretAM, // Base price didn't change
          oldFinalPrice: product.FinalPrice || product.PretAM, // Use base price if no previous final price
          newFinalPrice: finalPrice,
          reason: hasDiscount ? `Discount applied (${discountType})` : 'Discount removed',
          source: 'Server action',
          createdBy: 'system',
        },
      });
      
      logInfo(`Created price history record for ${product.Material_Number}`);
    }

    return true;
  } catch (error) {
    logError(`updateProductPrice error: ${error}`);
    return false;
  }
}


// export function calculateFinalPrice(
//   basePrice: Decimal | number,
//   discountType: DiscountType | null,
//   discountValue: Decimal | number | null
// ): Decimal {
//   // Convert to Decimal if needed
//   const basePriceDecimal = basePrice instanceof Decimal ? basePrice : new Decimal(basePrice);
  
//   // If no discount, return base price
//   if (!discountType || !discountValue) {
//     return basePriceDecimal;
//   }
  
//   const discountValueDecimal = discountValue instanceof Decimal 
//     ? discountValue 
//     : new Decimal(discountValue);
  
//   let finalPrice = basePriceDecimal;

//   switch (discountType) {
//     case "PERCENTAGE":
//       // Ensure percentage is within valid range (0-100)
//       const percentage = discountValueDecimal.toNumber();
//       if (percentage > 0 && percentage <= 100) {
//         finalPrice = basePriceDecimal.minus(
//           basePriceDecimal.mul(percentage).div(100)
//         );
//       }
//       break;

//     case "FIXED_AMOUNT":
//       // Ensure fixed amount doesn't make price negative
//       if (discountValueDecimal.greaterThan(0) && discountValueDecimal.lessThan(basePriceDecimal)) {
//         finalPrice = basePriceDecimal.minus(discountValueDecimal);
//         // Add debug logging
//         logInfo(`FIXED_AMOUNT: Base price ${basePriceDecimal} - discount ${discountValueDecimal} = final price ${finalPrice}`);
//       } else {
//         // If discount is invalid, don't apply it
//         logInfo(`FIXED_AMOUNT: Invalid discount value ${discountValueDecimal} for base price ${basePriceDecimal}`);
//       }
//       break;

//     case "NEW_PRICE":
//       // New price becomes final price if lower than base price
//       if (discountValueDecimal.greaterThan(0) && discountValueDecimal.lessThan(basePriceDecimal)) {
//         finalPrice = discountValueDecimal;
//       }
//       break;
//   }

//   return finalPrice;
// }

// export function calculateDiscountPercentage(
//   basePrice: Decimal | number,
//   finalPrice: Decimal | number
// ): Decimal {
//   // Convert to Decimal if needed
//   const basePriceDecimal = basePrice instanceof Decimal ? basePrice : new Decimal(basePrice);
//   const finalPriceDecimal = finalPrice instanceof Decimal ? finalPrice : new Decimal(finalPrice);
  
//   // If final price is higher or equal to base price, no discount
//   if (finalPriceDecimal.greaterThanOrEqualTo(basePriceDecimal) || basePriceDecimal.equals(0)) {
//     return new Decimal(0);
//   }
  
//   // Calculate percentage off
//   const discount = basePriceDecimal.minus(finalPriceDecimal);
//   const percentage = discount.div(basePriceDecimal).mul(100);
  
//   return percentage;
// }

// export async function updateProductPrice(productId: string): Promise<boolean> {
//   try {
//     // Get the product
//     const product = await prisma.product.findUnique({
//       where: { id: productId },
//       select: {
//         id: true,
//         PretAM: true,
//         FinalPrice: true,
//         Material_Number: true,
//       },
//     });

//     if (!product || !product.PretAM) {
//       logError(`updateProductPrice: Product ${productId} not found or has no base price`);
//       return false;
//     }

//     // Find active discount for this product
//     const productDiscount = await prisma.productDiscount.findFirst({
//       where: {
//         productId,
//         discount: {
//           active: true,
//           startDate: { lte: new Date() },
//           endDate: { gte: new Date() },
//         },
//       },
//       include: {
//         discount: {
//           select: {
//             id: true,
//             name: true,
//             type: true,
//             value: true,
//           },
//         },
//       },
//     });

//     // Calculate new price
//     let finalPrice: Decimal;
//     let hasDiscount = false;
//     let discountType = null;
//     let discountValue = null;
//     let discountPercentage = null;

//     if (productDiscount) {
//       // Apply discount
//       discountType = productDiscount.discount.type;
//       discountValue = productDiscount.discount.value;
      
//       logInfo(`Processing discount for product ${product.Material_Number}: ${discountType} discount with value ${discountValue}`);
      
//       finalPrice = calculateFinalPrice(
//         product.PretAM,
//         discountType,
//         discountValue
//       );
      
//       // Only mark as discounted if final price is actually lower
//       if (finalPrice.lessThan(product.PretAM)) {
//         hasDiscount = true;
//         discountPercentage = calculateDiscountPercentage(product.PretAM, finalPrice);
//         logInfo(`Discount applied: ${product.Material_Number} - Original: ${product.PretAM}, Final: ${finalPrice}, Discount %: ${discountPercentage}`);
//       } else {
//         // If discount doesn't actually reduce price, don't apply it
//         finalPrice = product.PretAM;
//         discountType = null;
//         discountValue = null;
//         logInfo(`Discount not applied: ${product.Material_Number} - Final price would not be lower`);
//       }
//     } else {
//       // No discount
//       finalPrice = product.PretAM;
//       logInfo(`No active discount found for product ${product.Material_Number}`);
//     }

//     // Calculate price range
//     let priceRange: string;
//     const finalPriceNum = finalPrice.toNumber();
    
//     if (finalPriceNum < 50) {
//       priceRange = '0-50';
//     } else if (finalPriceNum < 100) {
//       priceRange = '50-100';
//     } else if (finalPriceNum < 200) {
//       priceRange = '100-200';
//     } else if (finalPriceNum < 500) {
//       priceRange = '200-500';
//     } else {
//       priceRange = '500+';
//     }

//     // Check if price actually changed
//     const priceChanged = !product.FinalPrice || !product.FinalPrice.equals(finalPrice);
    
//     if (priceChanged) {
//       logInfo(`Price changed for ${product.Material_Number}: ${product.FinalPrice} -> ${finalPrice}`);
//     } else {
//       logInfo(`No price change for ${product.Material_Number}`);
//     }

//     // Update product with new price
//     await prisma.product.update({
//       where: { id: productId },
//       data: {
//         FinalPrice: finalPrice,
//         HasDiscount: hasDiscount,
//         activeDiscountType: discountType,
//         activeDiscountValue: discountValue,
//         discountPercentage: discountPercentage,
//         priceRange: priceRange,
//       },
//     });

//     // Log price change if needed
//     if (priceChanged) {
//       await prisma.priceHistory.create({
//         data: {
//           productId: product.id,
//           oldPretAM: product.PretAM,
//           newPretAM: product.PretAM, // Base price didn't change
//           oldFinalPrice: product.FinalPrice || product.PretAM, // Use base price if no previous final price
//           newFinalPrice: finalPrice,
//           reason: hasDiscount ? `Discount applied (${discountType})` : 'Discount removed',
//           source: 'Server action',
//           createdBy: 'system',
//         },
//       });
      
//       logInfo(`Created price history record for ${product.Material_Number}`);
//     }

//     return true;
//   } catch (error) {
//     logError(`updateProductPrice error: ${error}`);
//     return false;
//   }
// }


// export function calculateDiscountPercentage(
//   basePrice: Decimal | number,
//   finalPrice: Decimal | number
// ): Decimal {
//   // Convert to Decimal if needed
//   const basePriceDecimal = basePrice instanceof Decimal ? basePrice : new Decimal(basePrice);
//   const finalPriceDecimal = finalPrice instanceof Decimal ? finalPrice : new Decimal(finalPrice);
  
//   // If final price is higher or equal to base price, no discount
//   if (finalPriceDecimal.greaterThanOrEqualTo(basePriceDecimal) || basePriceDecimal.equals(0)) {
//     return new Decimal(0);
//   }
  
//   // Calculate percentage off
//   const discount = basePriceDecimal.minus(finalPriceDecimal);
//   const percentage = discount.div(basePriceDecimal).mul(100);
  
//   return percentage;
// }


// export function calculateFinalPrice(
//   basePrice: Decimal,
//   discountType: DiscountType,
//   discountValue: Decimal
// ): Decimal {
//   let finalPrice = basePrice;

//   switch (discountType) {
//     case "PERCENTAGE":
//       // Ensure that percentage is within a valid range (0-100)
//       const percentage = discountValue.toNumber();
//       if (percentage > 0 && percentage <= 100) {
//         finalPrice = basePrice.minus(basePrice.mul(percentage).div(100));
//       }
//       break;

//     case "FIXED_AMOUNT":
//       // Ensure that the fixed amount is not greater than the base price
//       const fixedAmount = discountValue.toNumber();
//       finalPrice = Decimal.max(basePrice.minus(fixedAmount), new Decimal(0));
//       break;

//     case "NEW_PRICE":
//       // The new price becomes the final price
//       finalPrice = discountValue;
//       break;
//   }

//   return finalPrice;
// }

// export async function updateProductPrice(productId: string): Promise<boolean> {
//   try {
//     // Get the product
//     const product = await prisma.product.findUnique({
//       where: { id: productId },
//       select: {
//         id: true,
//         PretAM: true,
//         FinalPrice: true,
//       },
//     });

//     if (!product || !product.PretAM) {
//       return false;
//     }

//     // Find active discount for this product
//     const productDiscount = await prisma.productDiscount.findFirst({
//       where: {
//         productId,
//         discount: {
//           active: true,
//           startDate: { lte: new Date() },
//           endDate: { gte: new Date() },
//         },
//       },
//       include: {
//         discount: {
//           select: {
//             type: true,
//             value: true,
//           },
//         },
//       },
//     });

//     // Calculate new price
//     let finalPrice: Decimal;
//     let hasDiscount = false;
//     let discountType = null;
//     let discountValue = null;
//     let discountPercentage = null;

//     if (productDiscount) {
//       // Apply discount
//       discountType = productDiscount.discount.type;
//       discountValue = productDiscount.discount.value;
//       finalPrice = calculateFinalPrice(
//         product.PretAM,
//         discountType,
//         discountValue
//       );

//       console.log(finalPrice);
      
//       // Only mark as discounted if final price is actually lower
//       if (finalPrice.lessThan(product.PretAM)) {
//         hasDiscount = true;
//         discountPercentage = calculateDiscountPercentage(product.PretAM, finalPrice);
//       } else {
//         // If discount doesn't actually reduce price, don't apply it
//         finalPrice = product.PretAM;
//         discountType = null;
//         discountValue = null;
//       }
//     } else {
//       // No discount
//       finalPrice = product.PretAM;
//     }

//     // Calculate price range
//     let priceRange: string;
//     const finalPriceNum = finalPrice.toNumber();
    
//     if (finalPriceNum < 50) {
//       priceRange = '0-50';
//     } else if (finalPriceNum < 100) {
//       priceRange = '50-100';
//     } else if (finalPriceNum < 200) {
//       priceRange = '100-200';
//     } else if (finalPriceNum < 500) {
//       priceRange = '200-500';
//     } else {
//       priceRange = '500+';
//     }

//     // Update product with new price
//     await prisma.product.update({
//       where: { id: productId },
//       data: {
//         FinalPrice: finalPrice,
//         HasDiscount: hasDiscount,
//         activeDiscountType: discountType,
//         activeDiscountValue: discountValue,
//         discountPercentage: discountPercentage,
//         priceRange: priceRange,
//       },
//     });

//     // Log price change if needed
//     if (product.FinalPrice && !product.FinalPrice.equals(finalPrice)) {
//       await prisma.priceHistory.create({
//         data: {
//           productId: product.id,
//           oldPretAM: product.PretAM,
//           newPretAM: product.PretAM, // Base price didn't change
//           oldFinalPrice: product.FinalPrice,
//           newFinalPrice: finalPrice,
//           reason: hasDiscount ? 'Discount applied' : 'Discount removed',
//           source: 'Server action',
//           createdBy: 'system',
//         },
//       });
//     }

//     logInfo(`updateProductPrice-> for product ${productId} updated with success with final price ${finalPrice} and hasDiscount ${hasDiscount}`);
//     return true;
//   } catch (error) {
//     logError(`updateProductPrice-> for product ${productId} error: ${error}`);
//     return false;
//   }
// }
