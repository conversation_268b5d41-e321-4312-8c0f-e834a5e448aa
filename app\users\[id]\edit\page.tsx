import { notFound } from "next/navigation";
import { ArrowLef<PERSON> } from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import UserForm from "@/app/components/user/UserForm";
import { getUserById } from "@/app/getData/user/data";
import { requireAdminOrModerator } from "@/lib/auth-utils";

interface EditUserPageProps {
  params: Promise<{ id: string }>;
}

export default async function EditUserPage({ params }: EditUserPageProps) {
  await requireAdminOrModerator();
  const paramsObject = await params;
  const user = await getUserById(paramsObject.id);
  
  if (!user) {
    notFound();
  }
  
  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center gap-4 mb-6">
        <Link href={`/users/${user.id}`}>
          <Button variant="outline" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-3xl font-bold">Edit User</h1>
      </div>
      
      <div className="grid grid-cols-1 gap-6 max-w-3xl">
        <div className="bg-white dark:bg-gray-950 p-6 rounded-lg border shadow-sm">
          <UserForm user={user} />
        </div>
      </div>
    </div>
  );
}