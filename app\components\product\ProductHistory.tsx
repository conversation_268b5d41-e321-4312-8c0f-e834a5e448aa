"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

import { ProductHistory } from "@/generated/prisma";


interface FoundProductHistoryInterface{
    foundProductHistory: ProductHistory[]
}

export default function ProductHistoryPage({ foundProductHistory }: FoundProductHistoryInterface) {
    return (
        <Card className="mb-4">
          <CardHeader>
            <CardTitle>History</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Material Number</TableHead>
                  <TableHead>Version</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>By</TableHead>
                  <TableHead>Changes</TableHead>
                  <TableHead>Snapshot</TableHead>
                  <TableHead>Updated At</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                  {foundProductHistory.map((history) => (
                    <TableRow key={history.id}>
                      <TableCell>{history.Material_Number}</TableCell>
                      <TableCell>{history.version}</TableCell>
                      <TableCell>{history.change_type}</TableCell>
                      <TableCell>{history.changed_by}</TableCell>
                      <TableCell  className="text-xs">{JSON.stringify(history.changes)}</TableCell>
                      <TableCell  className="text-xs">{JSON.stringify(history.snapshot)}</TableCell>
                      <TableCell>{new Date(history.updatedAt).toLocaleString()}</TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
    )
}