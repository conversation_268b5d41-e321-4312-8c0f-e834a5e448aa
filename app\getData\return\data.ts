import prisma from "@/app/utils/db";
import { ReturnStatus } from "@/generated/prisma";

export async function getReturnById(id: string) {
  try {
    const returnData = await prisma.return.findUnique({
      where: { id },
      include: {
        order: {
          include: {
            orderItems: true,
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        returnItems: {
          include: {
            orderItem: {
              include: {
                product: true,
              },
            },
          },
        },
        statusHistory: {
          orderBy: {
            createdAt: "desc",
          },
        },
      },
    });

    return returnData;
  } catch (error) {
    console.error("Error fetching return:", error);
    return null;
  }
}

export async function getReturns(limit = 50, status?: ReturnStatus) {
  try {
    const returns = await prisma.return.findMany({
      where: status ? { status } : undefined,
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
      include: {
        order: {
          select: {
            id: true,
            user: {
              select: {
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        returnItems: {
          include: {
            orderItem: {
              include: {
                product: {
                  select: {
                    Description_Local: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return returns;
  } catch (error) {
    console.error("Error fetching returns:", error);
    return [];
  }
}

export async function getReturnsByOrderId(orderId: string) {
  try {
    const returns = await prisma.return.findMany({
      where: { orderId },
      orderBy: {
        createdAt: "desc",
      },
      include: {
        returnItems: {
          include: {
            orderItem: {
              include: {
                product: true,
              },
            },
          },
        },
        statusHistory: {
          orderBy: {
            createdAt: "desc",
          },
          take: 1,
        },
      },
    });

    return returns;
  } catch (error) {
    console.error("Error fetching returns for order:", error);
    return [];
  }
}