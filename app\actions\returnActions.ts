"use server";

import { revalidatePath } from "next/cache";
import prisma from "@/app/utils/db";
import { logError, logInfo } from "@/lib/logger";
import { 
  CreateReturnFormValues,
  UpdateReturnFormValues,
  createReturnSchema,
  updateReturnSchema
} from "@/app/zod/returnSchemas";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { ReturnStatus } from "@prisma/client";
import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";

export type ReturnAction = {
  status: "SUCCESS" | "ERROR";
  message: string;
  data?: any;
  fieldErrors?: Record<string, string>;
};

export async function createReturn(data: CreateReturnFormValues): Promise<ReturnAction> {
  try {
    const { userId } = await auth();
    if (!userId) {
      return {
        status: "ERROR",
        message: "You must be logged in to create a return"
      };
    }

    // Validate input data
    const validationResult = createReturnSchema.safeParse(data);
    if (!validationResult.success) {
      const fieldErrors = Object.fromEntries(
        Object.entries(validationResult.error.flatten().fieldErrors).map(
          ([key, value]) => [key, value?.[0] || ""]
        )
      );
      
      return {
        status: "ERROR",
        message: "Validation error",
        fieldErrors
      };
    }

    const validatedData = validationResult.data;
    
    // Generate a return number
    const returnCount = await prisma.return.count();
    const returnNumber = `RET-${new Date().getFullYear()}-${(returnCount + 1).toString().padStart(5, '0')}`;
    
    // Create the return
    const newReturn = await prisma.return.create({
      data: {
        returnNumber,
        orderId: validatedData.orderId,
        reason: validatedData.reason,
        additionalNotes: validatedData.additionalNotes,
        status: ReturnStatus.requested,
        createdBy: userId,
        returnItems: {
          create: validatedData.items.map(item => ({
            orderItemId: item.orderItemId,
            quantity: item.quantity,
            reason: item.reason,
            description: item.description,
          }))
        }
      },
      include: {
        returnItems: true
      }
    });

    // Create initial status history
    await prisma.returnStatusHistory.create({
      data: {
        returnId: newReturn.id,
        newStatus: ReturnStatus.requested,
        changedBy: userId,
        notes: "Return request created"
      }
    });
    
    // Revalidate paths
    revalidatePath(`/orders/${validatedData.orderId}`);
    revalidatePath('/returns');
    
    logInfo(`Return created: ${newReturn.id} for order ${validatedData.orderId} by ${userId}`);
    
    return {
      status: "SUCCESS",
      message: "Return request created successfully",
      data: { returnId: newReturn.id }
    };
  } catch (error) {
    logError(`Error creating return: ${error}`);
    return {
      status: "ERROR",
      message: "Failed to create return request"
    };
  }
}

export async function updateReturnStatus(
  returnId: string, 
  newStatus: ReturnStatus, 
  notes?: string
): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email;
  
  try {
    const existingReturn = await prisma.return.findUnique({
      where: { id: returnId }
    });
    
    if (!existingReturn) {
      return {
        status: "ERROR",
        message: "Return not found"
      };
    }
    
    const previousStatus = existingReturn.status;
    
    // Update the return status
    const updatedReturn = await prisma.return.update({
      where: { id: returnId },
      data: {
        status: newStatus,
        updatedBy: actorEmail
      }
    });
    
    // Create status history entry
    await prisma.returnStatusHistory.create({
      data: {
        returnId,
        previousStatus,
        newStatus,
        notes,
        changedBy: actorEmail
      }
    });
    
    // Revalidate paths
    revalidatePath(`/returns/${returnId}`);
    revalidatePath('/returns');
    
    logInfo(`Return ${returnId} status updated from ${previousStatus} to ${newStatus} by ${actorEmail}`);
    
    return {
      status: "SUCCESS",
      message: "Return status updated successfully"
    };
  } catch (error) {
    logError(`Error updating return status: ${error} by ${actorEmail}`);
    return {
      status: "ERROR",
      message: "Failed to update return status"
    };
  }
}