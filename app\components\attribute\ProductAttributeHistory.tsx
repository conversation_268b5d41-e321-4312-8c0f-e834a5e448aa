
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

import { ProductAttributeHistory } from "@/generated/prisma";


interface FoundProductHistoryInterface{
    materialNumberAttributesHistory: ProductAttributeHistory[]
}

export default function ProductAttributeHistoryPage({ materialNumberAttributesHistory }: FoundProductHistoryInterface) {
    return (
        <Card>
          <CardHeader>
            <CardTitle>History</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Material Number</TableHead>
                  <TableHead>Version</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>By</TableHead>
                  <TableHead>Changes</TableHead>
                  <TableHead>Snapshot</TableHead>
                  <TableHead>Updated At</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                  {materialNumberAttributesHistory.map((history) => (
                    <TableRow key={history.id}>
                      <TableCell>{history.Material_Number}</TableCell>
                      <TableCell>{history.version}</TableCell>
                      <TableCell>{history.change_type}</TableCell>
                      <TableCell>{history.changed_by}</TableCell>
                      <TableCell  className="text-xs">{JSON.stringify(history.changes)}</TableCell>
                      <TableCell  className="text-xs">{JSON.stringify(history.snapshot)}</TableCell>
                      <TableCell>{new Date(history.updatedAt).toLocaleString()}</TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
    )
}