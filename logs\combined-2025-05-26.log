{"level":"info","message":"ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea","timestamp":"2025-05-26 13:35:19"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde by cosmin.oprea","timestamp":"2025-05-26 13:35:24"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,mac -- reasd by cosmin.oprea","timestamp":"2025-05-26 13:35:34"}
{"level":"error","message":"getProductAttributes -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 13:36:48"}
{"level":"error","message":"getProductAttributes -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 13:36:55"}
{"level":"error","message":"getProductAttributes -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 13:37:02"}
{"level":"error","message":"getProductAttributes -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 13:37:29"}
{"level":"error","message":"getProductAttributes -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 13:39:07"}
{"level":"error","message":"getProductAttributes -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 13:39:19"}
{"level":"error","message":"getProductAttributes -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 13:40:07"}
{"level":"error","message":"getProductAttributes -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 13:40:15"}
{"level":"error","message":"getProductAttributes -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 13:40:20"}
{"level":"error","message":"getProductAttributes -> Product \"01200426531\" not found by cosmin.oprea","timestamp":"2025-05-26 13:40:32"}
{"level":"info","message":"ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea","timestamp":"2025-05-26 13:40:59"}
{"level":"info","message":"ProductSearchAction -> Search for 01090398983 with success by cosmin.oprea","timestamp":"2025-05-26 13:41:08"}
{"level":"info","message":"ProductSearchAction -> Search for 01410012466 with success by cosmin.oprea","timestamp":"2025-05-26 13:41:17"}
{"level":"info","message":"ProductSearchAction -> Search for 01200411868 with success by cosmin.oprea","timestamp":"2025-05-26 13:41:35"}
{"level":"info","message":"getProductAttributes -> Product \"01200411868\" has the attributes:  by cosmin.oprea","timestamp":"2025-05-26 13:42:25"}
{"level":"error","message":"getProductAttributes -> Error at parsing: [\n  {\n    \"code\": \"too_big\",\n    \"maximum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 13:43:04"}
{"level":"info","message":"ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea","timestamp":"2025-05-26 13:43:46"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,mac -- reasd by cosmin.oprea","timestamp":"2025-05-26 13:43:52"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,mac -- reasd by cosmin.oprea","timestamp":"2025-05-26 13:47:23"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,mac -- reasd by cosmin.oprea","timestamp":"2025-05-26 13:48:22"}
{"level":"info","message":"ProductAddAttributeCSVAction -> ","timestamp":"2025-05-26 14:00:02"}
{"level":"info","message":"ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea","timestamp":"2025-05-26 14:00:17"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,mac -- reasd,key1 -- value1 by cosmin.oprea","timestamp":"2025-05-26 14:00:19"}
{"level":"info","message":"getProductAttributes -> Product \"01090398983\" has the attributes: mac -- 123,key2 -- value2 by cosmin.oprea","timestamp":"2025-05-26 14:00:29"}
{"level":"info","message":"getProductAttributes -> Product \"01410012466\" has the attributes: key3 -- value3 by cosmin.oprea","timestamp":"2025-05-26 14:00:35"}
{"level":"error","message":"getProductAttributes -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 14:00:38"}
{"level":"info","message":"getProductAttributes -> Product \"01200411868\" has the attributes: key4 -- value4 by cosmin.oprea","timestamp":"2025-05-26 14:00:44"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      4,\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 14:01:05"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Attribute with key \"key1\" already exists for this product by cosmin.oprea","timestamp":"2025-05-26 14:02:13"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Attribute with key \"key1\" already exists for this product by cosmin.oprea","timestamp":"2025-05-26 14:05:53"}
{"level":"info","message":"ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea","timestamp":"2025-05-26 14:06:17"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,mac -- reasd,key1 -- value1 by cosmin.oprea","timestamp":"2025-05-26 14:06:25"}
{"level":"info","message":"ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea","timestamp":"2025-05-26 14:08:02"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,mac -- reasd,key1 -- value1 by cosmin.oprea","timestamp":"2025-05-26 14:08:31"}
{"level":"info","message":"ProductSearchAction -> Search for 01090398983 with success by cosmin.oprea","timestamp":"2025-05-26 14:08:43"}
{"level":"info","message":"getProductAttributes -> Product \"01090398983\" has the attributes: mac -- 123,key2 -- value2 by cosmin.oprea","timestamp":"2025-05-26 14:10:10"}
{"level":"info","message":"getProductAttributes -> Product \"01090398983\" has the attributes: mac -- 123,key2 -- value2 by cosmin.oprea","timestamp":"2025-05-26 14:11:16"}
{"level":"info","message":"getProductAttributes -> Product \"01090398983\" has the attributes: mac -- 123,key2 -- value2 by cosmin.oprea","timestamp":"2025-05-26 14:11:19"}
{"level":"info","message":"ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea","timestamp":"2025-05-26 14:13:24"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,mac -- reasd,key1 -- value1 by cosmin.oprea","timestamp":"2025-05-26 14:13:26"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,mac -- reasd,key1 -- value1 by cosmin.oprea","timestamp":"2025-05-26 14:13:35"}
{"level":"info","message":"ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea","timestamp":"2025-05-26 14:14:42"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,mac -- reasd,key1 -- value1 by cosmin.oprea","timestamp":"2025-05-26 14:34:42"}
{"level":"info","message":"DeleteProductAttributeAction -> deleted with success the key key1 for the product 11127502650 by cosmin.oprea","timestamp":"2025-05-26 14:34:51"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,mac -- reasd by cosmin.oprea","timestamp":"2025-05-26 14:34:51"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,mac -- reasd by cosmin.oprea","timestamp":"2025-05-26 14:34:53"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,mac -- reasd,dddd -- aaaa by cosmin.oprea","timestamp":"2025-05-26 14:35:01"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,mac -- reasd,dddd -- aaaa by cosmin.oprea","timestamp":"2025-05-26 14:35:03"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,mac -- reasd,dddd -- aaaa by cosmin.oprea","timestamp":"2025-05-26 14:43:37"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:43:37"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,mac -- reasd,dddd -- aaaa by cosmin.oprea","timestamp":"2025-05-26 14:43:40"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:43:40"}
{"level":"info","message":"DeleteProductAttributeAction -> deleted with success the key dddd for the product 11127502650 by cosmin.oprea","timestamp":"2025-05-26 14:45:02"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,mac -- reasd by cosmin.oprea","timestamp":"2025-05-26 14:45:02"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:45:02"}
{"level":"info","message":"DeleteProductAttributeAction -> deleted with success the key mac for the product 11127502650 by cosmin.oprea","timestamp":"2025-05-26 14:45:05"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde by cosmin.oprea","timestamp":"2025-05-26 14:45:05"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:45:05"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,xxxx -- tttt by cosmin.oprea","timestamp":"2025-05-26 14:45:08"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:45:08"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,xxxx -- tttt,asd -- asd by cosmin.oprea","timestamp":"2025-05-26 14:45:17"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:45:17"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,xxxx -- tttt,asd -- asd,dddd -- dddd by cosmin.oprea","timestamp":"2025-05-26 14:45:32"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:45:32"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,xxxx -- tttt,asd -- asd,dddd -- dddd by cosmin.oprea","timestamp":"2025-05-26 14:48:13"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:48:13"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,xxxx -- tttt,asd -- asd,dddd -- dddd by cosmin.oprea","timestamp":"2025-05-26 14:48:45"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:48:46"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,xxxx -- tttt,asd -- asd,dddd -- dddd by cosmin.oprea","timestamp":"2025-05-26 14:49:02"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:49:02"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,xxxx -- tttt,asd -- asd,dddd -- dddd by cosmin.oprea","timestamp":"2025-05-26 14:50:00"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:50:00"}
{"level":"info","message":"DeleteProductAttributeAction -> deleted with success the key dddd for the product 11127502650 by cosmin.oprea","timestamp":"2025-05-26 14:50:04"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,xxxx -- tttt,asd -- asd by cosmin.oprea","timestamp":"2025-05-26 14:50:04"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:50:04"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,xxxx -- tttt,asd -- asd,111 -- 2232 by cosmin.oprea","timestamp":"2025-05-26 14:50:11"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:50:11"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,xxxx -- tttt,asd -- asd,111 -- 2232,asdd -- asd by cosmin.oprea","timestamp":"2025-05-26 14:51:35"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:51:35"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,xxxx -- tttt,asd -- asd,111 -- 2232,asdd -- asd by cosmin.oprea","timestamp":"2025-05-26 14:51:51"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:51:51"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,xxxx -- tttt,asd -- asd,111 -- 2232,asdd -- asd by cosmin.oprea","timestamp":"2025-05-26 14:52:24"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:52:24"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,xxxx -- tttt,asd -- asd,111 -- 2232,asdd -- asd by cosmin.oprea","timestamp":"2025-05-26 14:55:09"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:55:09"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,xxxx -- tttt,asd -- asd,111 -- 2232,asdd -- asd by cosmin.oprea","timestamp":"2025-05-26 14:55:12"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:55:12"}
{"level":"info","message":"DeleteProductAttributeAction -> deleted with success the key asdd for the product 11127502650 by cosmin.oprea","timestamp":"2025-05-26 14:55:16"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,xxxx -- tttt,asd -- asd,111 -- 2232 by cosmin.oprea","timestamp":"2025-05-26 14:55:16"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:55:16"}
{"level":"info","message":"DeleteProductAttributeAction -> deleted with success the key 111 for the product 11127502650 by cosmin.oprea","timestamp":"2025-05-26 14:55:20"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,xxxx -- tttt,asd -- asd by cosmin.oprea","timestamp":"2025-05-26 14:55:20"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:55:20"}
{"level":"info","message":"DeleteProductAttributeAction -> deleted with success the key asd for the product 11127502650 by cosmin.oprea","timestamp":"2025-05-26 14:55:26"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,xxxx -- tttt by cosmin.oprea","timestamp":"2025-05-26 14:55:26"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:55:27"}
{"level":"info","message":"DeleteProductAttributeAction -> deleted with success the key xxxx for the product 11127502650 by cosmin.oprea","timestamp":"2025-05-26 14:55:29"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde by cosmin.oprea","timestamp":"2025-05-26 14:55:29"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:55:29"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde,key1 -- value1 by cosmin.oprea","timestamp":"2025-05-26 14:55:41"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:55:41"}
{"level":"info","message":"DeleteProductAttributeAction -> deleted with success the key key1 for the product 11127502650 by cosmin.oprea","timestamp":"2025-05-26 14:56:30"}
{"level":"info","message":"getProductAttributes -> Product \"11127502650\" has the attributes: color -- verde by cosmin.oprea","timestamp":"2025-05-26 14:56:30"}
{"level":"info","message":"getProductAttributesHistory -> Product \"11127502650\" with success by cosmin.oprea","timestamp":"2025-05-26 14:56:30"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmb0pfvsi0004hxls1bere8yb updated successfully by cosmin.oprea","timestamp":"2025-05-26 14:56:49"}
{"level":"info","message":"ProductSearchAction -> Search for 01090398983 with success by cosmin.oprea","timestamp":"2025-05-26 15:17:12"}
{"level":"info","message":"getProductAttributes -> Product \"01090398983\" has the attributes: mac -- 123,key2 -- value2 by cosmin.oprea","timestamp":"2025-05-26 15:17:34"}
{"level":"info","message":"getProductAttributesHistory -> Product \"01090398983\" with success by cosmin.oprea","timestamp":"2025-05-26 15:17:34"}
{"level":"info","message":"getProductAttributes -> Product \"01090398983\" has the attributes: mac -- 123,key2 -- value2,xxx -- cccc by cosmin.oprea","timestamp":"2025-05-26 15:17:42"}
{"level":"info","message":"getProductAttributesHistory -> Product \"01090398983\" with success by cosmin.oprea","timestamp":"2025-05-26 15:17:42"}
{"level":"info","message":"DeleteProductAttributeAction -> deleted with success the key mac for the product 01090398983 by cosmin.oprea","timestamp":"2025-05-26 15:17:53"}
{"level":"info","message":"getProductAttributes -> Product \"01090398983\" has the attributes: key2 -- value2,xxx -- cccc by cosmin.oprea","timestamp":"2025-05-26 15:17:53"}
{"level":"info","message":"getProductAttributesHistory -> Product \"01090398983\" with success by cosmin.oprea","timestamp":"2025-05-26 15:17:53"}
{"level":"info","message":"DeleteProductAttributeAction -> deleted with success the key key2 for the product 01090398983 by cosmin.oprea","timestamp":"2025-05-26 15:17:55"}
{"level":"info","message":"getProductAttributes -> Product \"01090398983\" has the attributes: xxx -- cccc by cosmin.oprea","timestamp":"2025-05-26 15:17:55"}
{"level":"info","message":"getProductAttributesHistory -> Product \"01090398983\" with success by cosmin.oprea","timestamp":"2025-05-26 15:17:55"}
{"level":"info","message":"DeleteProductAttributeAction -> deleted with success the key xxx for the product 01090398983 by cosmin.oprea","timestamp":"2025-05-26 15:17:57"}
{"level":"info","message":"getProductAttributes -> Product \"01090398983\" has the attributes:  by cosmin.oprea","timestamp":"2025-05-26 15:17:57"}
{"level":"info","message":"getProductAttributesHistory -> Product \"01090398983\" with success by cosmin.oprea","timestamp":"2025-05-26 15:17:57"}
{"level":"info","message":"getProductAttributes -> Product \"01090398983\" has the attributes: test1 -- test2 by cosmin.oprea","timestamp":"2025-05-26 15:18:08"}
{"level":"info","message":"getProductAttributesHistory -> Product \"01090398983\" with success by cosmin.oprea","timestamp":"2025-05-26 15:18:08"}
{"level":"info","message":"DeleteProductAttributeAction -> deleted with success the key test1 for the product 01090398983 by cosmin.oprea","timestamp":"2025-05-26 15:18:20"}
{"level":"info","message":"getProductAttributes -> Product \"01090398983\" has the attributes:  by cosmin.oprea","timestamp":"2025-05-26 15:18:20"}
{"level":"info","message":"getProductAttributesHistory -> Product \"01090398983\" with success by cosmin.oprea","timestamp":"2025-05-26 15:18:20"}
{"level":"info","message":"ProductSearchAction -> Search for 01410012466 with success by cosmin.oprea","timestamp":"2025-05-26 15:19:22"}
{"level":"info","message":"ProductSearchAction -> Search for 01090398983 with success by cosmin.oprea","timestamp":"2025-05-26 15:20:16"}
{"level":"info","message":"getProductAttributes -> Product \"01090398983\" has the attributes:  by cosmin.oprea","timestamp":"2025-05-26 15:20:27"}
{"level":"info","message":"getProductAttributesHistory -> Product \"01090398983\" with success by cosmin.oprea","timestamp":"2025-05-26 15:20:27"}
{"level":"info","message":"Discount with name aaaaaaa created successfully by cosmin.oprea","timestamp":"2025-05-26 15:50:05"}
{"level":"info","message":"Discount with name jghhjhgj created successfully by cosmin.oprea","timestamp":"2025-05-26 15:52:48"}
{"level":"info","message":"Discount with name Test3 updated successfully by cosmin.oprea","timestamp":"2025-05-26 16:01:59"}
{"level":"error","message":"DiscountUpdateAction -> The discount allready exists: haha  by cosmin.oprea","timestamp":"2025-05-26 16:02:47"}
{"level":"info","message":"Discount with name haha updated successfully by cosmin.oprea","timestamp":"2025-05-26 16:02:47"}
{"level":"error","message":"DiscountUpdateAction -> The discount allready exists: haha2  by cosmin.oprea","timestamp":"2025-05-26 16:03:36"}
{"level":"info","message":"Discount with name haha2 updated successfully by cosmin.oprea","timestamp":"2025-05-26 16:03:36"}
{"level":"error","message":"DiscountUpdateAction -> The discount allready exists: bbbbbbbbbb  by cosmin.oprea","timestamp":"2025-05-26 16:04:15"}
{"level":"info","message":"Discount with name bbbbbbbbbb updated successfully by cosmin.oprea","timestamp":"2025-05-26 16:04:15"}
{"level":"error","message":"DiscountUpdateAction -> The discount allready exists: cccccc  by cosmin.oprea","timestamp":"2025-05-26 16:09:03"}
{"level":"info","message":"Discount with name cccccc updated successfully by cosmin.oprea","timestamp":"2025-05-26 16:09:03"}
{"level":"error","message":"DiscountUpdateAction -> The discount allready exists: ddddddddddddddddddddddddddd  by cosmin.oprea","timestamp":"2025-05-26 16:16:39"}
{"level":"info","message":"Discount with name ddddddddddddddddddddddddddd updated successfully by cosmin.oprea","timestamp":"2025-05-26 16:16:39"}
{"level":"info","message":"Discount with name haha33333 updated successfully by cosmin.oprea","timestamp":"2025-05-26 16:27:52"}
{"level":"info","message":"Discount with name haha44444 updated successfully by cosmin.oprea","timestamp":"2025-05-26 16:30:07"}
{"level":"info","message":"Discount with name nnnnnnnnnnnnnnn created successfully by cosmin.oprea","timestamp":"2025-05-26 16:41:54"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmb0j303100rnrshx5i8h5leh for the discountId cmb0pfvsi0004hxls1bere8yb by cosmin.oprea","timestamp":"2025-05-26 17:14:05"}
