import { currentUser } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import UserSecuritySettings from "@/app/components/user/UserSecuritySettings";
import { requireAdminOrModerator } from "@/lib/auth-utils";

export default async function SettingsPage() {
  const dbUser = await requireAdminOrModerator();
  
  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold">Account Settings</h1>
          <p className="text-gray-500 mt-1">Manage your account preferences</p>
        </div>
      </div>
      
      <div className="grid grid-cols-1 gap-6 max-w-3xl">
        <Card>
          <CardHeader>
            <CardTitle>Notifications</CardTitle>
            <CardDescription>
              Configure how you receive notifications
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="newsletter" className="flex flex-col gap-1">
                  <span>Newsletter</span>
                  <span className="font-normal text-sm text-gray-500">Receive updates about new features and promotions</span>
                </Label>
                <Switch id="newsletter" defaultChecked={dbUser.newsletterOptIn} />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="security-alerts" className="flex flex-col gap-1">
                  <span>Security Alerts</span>
                  <span className="font-normal text-sm text-gray-500">Get notified about important security events</span>
                </Label>
                <Switch id="security-alerts" defaultChecked={true} />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <UserSecuritySettings 
          userId={dbUser.id} 
          lastLoginAt={dbUser.lastLoginAt?.toString()} 
        />
      </div>
    </div>
  );
}