import { Metadata } from "next";
import { notFound } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { getGroupById } from "@/app/getData/groups/data";
import EditGroupClient from "./EditGroupClient";

interface EditGroupPageProps {
  params: Promise<{
    id: string;
  }>;
}

export async function generateMetadata({ params }: EditGroupPageProps): Promise<Metadata> {
  const paramsObject = await params;
  const group = await getGroupById(paramsObject.id);

  return {
    title: group ? `Edit ${group.name} - Access Group` : "Edit Group",
    description: group?.description || "Edit access group details",
  };
}

export default async function EditGroupPage({ params }: EditGroupPageProps) {
  await requireAdminOrModerator();
  const paramsObject = await params;
  const group = await getGroupById(paramsObject.id);

  if (!group) {
    notFound();
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center gap-4 mb-6">
        <Link href={`/groups/${group.id}`}>
          <Button variant="outline" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-3xl font-bold">Edit Access Group</h1>
      </div>

      <div className="max-w-2xl">
        <EditGroupClient group={group} />
      </div>
    </div>
  );
}
