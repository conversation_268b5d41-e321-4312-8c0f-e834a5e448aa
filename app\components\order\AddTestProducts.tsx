"use client";

import { useState, useTransition } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { addTestProductsToOrder } from "@/app/actions/testActions";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface AddTestProductsProps {
  orderId: string;
}

export default function AddTestProducts({ orderId }: AddTestProductsProps) {
  const [isPending, startTransition] = useTransition();
  const [open, setOpen] = useState(false);
  const [count, setCount] = useState(3);

  const handleAddProducts = () => {
    startTransition(async () => {
      try {
        const response = await addTestProductsToOrder(orderId, count);
        
        if (response.status === "SUCCESS") {
          toast.success(response.message);
          setOpen(false);
        } else {
          toast.error(response.message);
        }
      } catch (error) {
        toast.error("An unexpected error occurred");
        console.error(error);
      }
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          Add Test Products
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Test Products</DialogTitle>
          <DialogDescription>
            Add random products to this order for testing purposes.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="count" className="text-right">
              Count
            </Label>
            <Input
              id="count"
              type="number"
              min={1}
              max={10}
              value={count}
              onChange={(e) => setCount(parseInt(e.target.value) || 1)}
              className="col-span-3"
            />
          </div>
        </div>
        <DialogFooter>
          <Button 
            onClick={handleAddProducts} 
            disabled={isPending}
          >
            {isPending ? "Adding..." : "Add Products"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}