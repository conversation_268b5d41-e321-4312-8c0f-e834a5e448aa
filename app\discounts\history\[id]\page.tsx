import { getDiscountHistory } from "@/app/getData/discount/data";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { requireAdminOrModerator } from "@/lib/auth-utils";


export default async function DiscountHistoryRoute({ params }: { params: Promise<{ id: string }> }) {
    await requireAdminOrModerator();
    const paramsObject = await params;
    const idAsString = paramsObject.id;    //All params are strings and need to be parsed
    const id = idAsString

    const discountHistory = await getDiscountHistory(id)

    return (
        <Card>
          <CardHeader>
            <CardTitle>History</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Version</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>By</TableHead>
                  <TableHead>Changes</TableHead>
                  <TableHead>Snapshot</TableHead>
                  <TableHead>Updated At</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                  {discountHistory.map((history) => (
                    <TableRow key={history.id}>
                      <TableCell>{history.discount.name}</TableCell>
                      <TableCell>{history.version}</TableCell>
                      <TableCell>{history.change_type}</TableCell>
                      <TableCell>{history.changed_by}</TableCell>
                      <TableCell  className="text-xs">{JSON.stringify(history.changes)}</TableCell>
                      <TableCell  className="text-xs">{JSON.stringify(history.snapshot)}</TableCell>
                      <TableCell>{new Date(history.updatedAt).toLocaleString()}</TableCell>
                    </TableRow>
                  ))} 
              </TableBody>
            </Table>
          </CardContent>
        </Card>
    )
}