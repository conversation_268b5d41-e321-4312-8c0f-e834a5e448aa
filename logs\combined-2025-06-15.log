{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:05:01"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbuoiyb205lt9whxhklceafb (12317501755)","timestamp":"2025-06-15 00:05:59"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiyb205lt9whxhklceafb","timestamp":"2025-06-15 00:05:59"}
{"level":"error","message":"updateProductPrice: Product cmbuoiyb205lt9whxhklceafb not found or has no base price","timestamp":"2025-06-15 00:05:59"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Failed","timestamp":"2025-06-15 00:05:59"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbuqdkl00003hxv4vm6utec5 updated successfully by unauthenticatedUser","timestamp":"2025-06-15 00:05:59"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:06:00"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiyb205lt9whxhklceafb","timestamp":"2025-06-15 00:06:03"}
{"level":"error","message":"updateProductPrice: Product cmbuoiyb205lt9whxhklceafb not found or has no base price","timestamp":"2025-06-15 00:06:03"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbuoiyb205lt9whxhklceafb for the discountId cmbuqdkl00003hxv4vm6utec5 by unauthenticatedUser","timestamp":"2025-06-15 00:06:03"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:06:03"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:32:35"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbuoiy9f05k79whxbc5eh2gg (11531743192)","timestamp":"2025-06-15 00:32:40"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiy9f05k79whxbc5eh2gg","timestamp":"2025-06-15 00:32:40"}
{"level":"info","message":"Found product 11531743192 with base price 24","timestamp":"2025-06-15 00:32:40"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-15 00:32:40"}
{"level":"info","message":"Price changed for 11531743192: none -> 24","timestamp":"2025-06-15 00:32:40"}
{"level":"info","message":"Updated product 11531743192 with new price 24","timestamp":"2025-06-15 00:32:40"}
{"level":"info","message":"Created price history record for 11531743192","timestamp":"2025-06-15 00:32:40"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-15 00:32:40"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbuqdkl00003hxv4vm6utec5 updated successfully by unauthenticatedUser","timestamp":"2025-06-15 00:32:40"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:32:41"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbuoiyb205lt9whxhklceafb (12317501755)","timestamp":"2025-06-15 00:32:49"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiyb205lt9whxhklceafb","timestamp":"2025-06-15 00:32:49"}
{"level":"info","message":"Found product 12317501755 with base price 25","timestamp":"2025-06-15 00:32:49"}
{"level":"info","message":"No active discount found for product 12317501755","timestamp":"2025-06-15 00:32:49"}
{"level":"info","message":"Price changed for 12317501755: none -> 25","timestamp":"2025-06-15 00:32:49"}
{"level":"info","message":"Updated product 12317501755 with new price 25","timestamp":"2025-06-15 00:32:49"}
{"level":"info","message":"Created price history record for 12317501755","timestamp":"2025-06-15 00:32:49"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-15 00:32:49"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbuqdkl00003hxv4vm6utec5 updated successfully by unauthenticatedUser","timestamp":"2025-06-15 00:32:49"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:32:50"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiy9f05k79whxbc5eh2gg","timestamp":"2025-06-15 00:36:14"}
{"level":"info","message":"Found product 11531743192 with base price 24","timestamp":"2025-06-15 00:36:14"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-15 00:36:14"}
{"level":"info","message":"No price change for 11531743192: 24","timestamp":"2025-06-15 00:36:14"}
{"level":"info","message":"Updated product 11531743192 with new price 24","timestamp":"2025-06-15 00:36:15"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbuoiy9f05k79whxbc5eh2gg for the discountId cmbuqdkl00003hxv4vm6utec5 by unauthenticatedUser","timestamp":"2025-06-15 00:36:15"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:36:15"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiyb205lt9whxhklceafb","timestamp":"2025-06-15 00:36:18"}
{"level":"info","message":"Found product 12317501755 with base price 25","timestamp":"2025-06-15 00:36:18"}
{"level":"info","message":"No active discount found for product 12317501755","timestamp":"2025-06-15 00:36:18"}
{"level":"info","message":"No price change for 12317501755: 25","timestamp":"2025-06-15 00:36:18"}
{"level":"info","message":"Updated product 12317501755 with new price 25","timestamp":"2025-06-15 00:36:18"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbuoiyb205lt9whxhklceafb for the discountId cmbuqdkl00003hxv4vm6utec5 by unauthenticatedUser","timestamp":"2025-06-15 00:36:18"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:36:18"}
{"level":"error","message":"DiscountProcessCSVAction -> No material numbers provided  by unauthenticatedUser","timestamp":"2025-06-15 00:38:02"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:38:03"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:39:59"}
{"level":"error","message":"DiscountProcessCSVAction -> No material numbers provided  by unauthenticatedUser","timestamp":"2025-06-15 00:40:04"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:40:05"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:41:04"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:41:05"}
{"level":"error","message":"DiscountProcessCSVAction -> No material numbers provided  by unauthenticatedUser","timestamp":"2025-06-15 00:41:21"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:41:22"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:43:55"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:43:56"}
{"level":"error","message":"DiscountProcessCSVAction -> No material numbers provided  by <EMAIL>","timestamp":"2025-06-15 00:44:51"}
{"level":"error","message":"DiscountProcessCSVAction -> No material numbers provided  by <EMAIL>","timestamp":"2025-06-15 00:46:19"}
{"level":"error","message":"DiscountProcessCSVAction -> No material numbers provided  by <EMAIL>","timestamp":"2025-06-15 00:47:54"}
{"level":"error","message":"DiscountProcessCSVAction -> No material numbers provided  by <EMAIL>","timestamp":"2025-06-15 00:49:01"}
{"level":"error","message":"DiscountProcessCSVAction -> No material numbers provided  by <EMAIL>","timestamp":"2025-06-15 00:50:18"}
{"level":"info","message":"DiscountProcessCSVAction -> Success for discount cmbuqdkl00003hxv4vm6utec5 with products: 01090398983, 01410012466, 01200426532 by <EMAIL>","timestamp":"2025-06-15 01:04:08"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiua200009whxgr3pg7p4","timestamp":"2025-06-15 01:09:31"}
{"level":"error","message":"updateProductPrice: Product cmbuoiua200009whxgr3pg7p4 not found or has no base price","timestamp":"2025-06-15 01:09:31"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbuoiua200009whxgr3pg7p4 for the discountId cmbuqdkl00003hxv4vm6utec5 by <EMAIL>","timestamp":"2025-06-15 01:09:31"}
{"level":"info","message":"DiscountDeleteAllProductsAction -> deleted with success 2 products from the discountId cmbuqdkl00003hxv4vm6utec5  by <EMAIL>","timestamp":"2025-06-15 01:09:34"}
{"level":"info","message":"DiscountProcessCSVAction -> Success for discount cmbuqdkl00003hxv4vm6utec5 with products: 12317501755, 11531743192, 11000435439 by <EMAIL>","timestamp":"2025-06-15 01:11:11"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiucn004r9whxa4osg2qv","timestamp":"2025-06-15 01:11:33"}
{"level":"info","message":"Found product 11000435439 with base price 23","timestamp":"2025-06-15 01:11:33"}
{"level":"info","message":"No active discount found for product 11000435439","timestamp":"2025-06-15 01:11:33"}
{"level":"info","message":"Price changed for 11000435439: none -> 23","timestamp":"2025-06-15 01:11:33"}
{"level":"info","message":"Updated product 11000435439 with new price 23","timestamp":"2025-06-15 01:11:33"}
{"level":"info","message":"Created price history record for 11000435439","timestamp":"2025-06-15 01:11:33"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbuoiucn004r9whxa4osg2qv for the discountId cmbuqdkl00003hxv4vm6utec5 by <EMAIL>","timestamp":"2025-06-15 01:11:33"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiy9f05k79whxbc5eh2gg","timestamp":"2025-06-15 01:11:37"}
{"level":"info","message":"Found product 11531743192 with base price 24","timestamp":"2025-06-15 01:11:37"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-15 01:11:37"}
{"level":"info","message":"No price change for 11531743192: 24","timestamp":"2025-06-15 01:11:37"}
{"level":"info","message":"Updated product 11531743192 with new price 24","timestamp":"2025-06-15 01:11:37"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbuoiy9f05k79whxbc5eh2gg for the discountId cmbuqdkl00003hxv4vm6utec5 by <EMAIL>","timestamp":"2025-06-15 01:11:37"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiyb205lt9whxhklceafb","timestamp":"2025-06-15 01:11:40"}
{"level":"info","message":"Found product 12317501755 with base price 25","timestamp":"2025-06-15 01:11:40"}
{"level":"info","message":"No active discount found for product 12317501755","timestamp":"2025-06-15 01:11:40"}
{"level":"info","message":"No price change for 12317501755: 25","timestamp":"2025-06-15 01:11:40"}
{"level":"info","message":"Updated product 12317501755 with new price 25","timestamp":"2025-06-15 01:11:40"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbuoiyb205lt9whxhklceafb for the discountId cmbuqdkl00003hxv4vm6utec5 by <EMAIL>","timestamp":"2025-06-15 01:11:40"}
{"level":"info","message":"DiscountProcessCSVAction -> Success for discount cmbuqdkl00003hxv4vm6utec5 with products: 12317501755, 11531743192, 11000435439 by <EMAIL>","timestamp":"2025-06-15 01:19:04"}
{"level":"info","message":"DiscountDeleteAllProductsAction -> deleted with success 3 products from the discountId cmbuqdkl00003hxv4vm6utec5  by <EMAIL>","timestamp":"2025-06-15 01:19:20"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbuoiy9f05k79whxbc5eh2gg (11531743192)","timestamp":"2025-06-15 01:49:06"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiy9f05k79whxbc5eh2gg","timestamp":"2025-06-15 01:49:06"}
{"level":"info","message":"Found product 11531743192 with base price 124","timestamp":"2025-06-15 01:49:06"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-15 01:49:06"}
{"level":"info","message":"No price change for 11531743192: 124","timestamp":"2025-06-15 01:49:06"}
{"level":"info","message":"Updated product 11531743192 with new price 124","timestamp":"2025-06-15 01:49:06"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-15 01:49:06"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbuqdkl00003hxv4vm6utec5 updated <NAME_EMAIL>","timestamp":"2025-06-15 01:49:06"}
{"level":"error","message":"DiscountAddProductAction -> Product \"11531743192\" already has a <NAME_EMAIL>","timestamp":"2025-06-15 02:22:08"}
{"level":"info","message":"DiscountDeleteAllProductsAction -> deleted with success 1 products from the discountId cmbuqdkl00003hxv4vm6utec5  by <EMAIL>","timestamp":"2025-06-15 02:22:14"}
{"level":"info","message":"Discount with name Summer tyres updated <NAME_EMAIL>","timestamp":"2025-06-15 02:22:37"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbuoiy9f05k79whxbc5eh2gg (11531743192)","timestamp":"2025-06-15 02:22:53"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiy9f05k79whxbc5eh2gg","timestamp":"2025-06-15 02:22:53"}
{"level":"info","message":"Found product 11531743192 with base price 657","timestamp":"2025-06-15 02:22:53"}
{"level":"info","message":"Found active discount for 11531743192: PERCENTAGE with value 25","timestamp":"2025-06-15 02:22:53"}
{"level":"info","message":"PERCENTAGE: 657 - 25% = 492.75","timestamp":"2025-06-15 02:22:53"}
{"level":"info","message":"Discount applied: 11531743192 - Original: 657, Final: 492.75, Discount %: 25","timestamp":"2025-06-15 02:22:53"}
{"level":"info","message":"Price changed for 11531743192: 657 -> 492.75","timestamp":"2025-06-15 02:22:53"}
{"level":"info","message":"Updated product 11531743192 with new price 492.75","timestamp":"2025-06-15 02:22:53"}
{"level":"info","message":"Created price history record for 11531743192","timestamp":"2025-06-15 02:22:53"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-15 02:22:53"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbuqdkl00003hxv4vm6utec5 updated <NAME_EMAIL>","timestamp":"2025-06-15 02:22:53"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbuoiucn004r9whxa4osg2qv (11000435439)","timestamp":"2025-06-15 02:23:13"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiucn004r9whxa4osg2qv","timestamp":"2025-06-15 02:23:13"}
{"level":"info","message":"Found product 11000435439 with base price 234","timestamp":"2025-06-15 02:23:13"}
{"level":"info","message":"Found active discount for 11000435439: PERCENTAGE with value 25","timestamp":"2025-06-15 02:23:13"}
{"level":"info","message":"PERCENTAGE: 234 - 25% = 175.5","timestamp":"2025-06-15 02:23:13"}
{"level":"info","message":"Discount applied: 11000435439 - Original: 234, Final: 175.5, Discount %: 25","timestamp":"2025-06-15 02:23:13"}
{"level":"info","message":"Price changed for 11000435439: 234 -> 175.5","timestamp":"2025-06-15 02:23:13"}
{"level":"info","message":"Updated product 11000435439 with new price 175.5","timestamp":"2025-06-15 02:23:14"}
{"level":"info","message":"Created price history record for 11000435439","timestamp":"2025-06-15 02:23:14"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-15 02:23:14"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbuqdkl00003hxv4vm6utec5 updated <NAME_EMAIL>","timestamp":"2025-06-15 02:23:14"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbuoiuaj00069whx25ashkot (01200426532)","timestamp":"2025-06-15 02:23:43"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiuaj00069whx25ashkot","timestamp":"2025-06-15 02:23:43"}
{"level":"info","message":"Found product 01200426532 with base price 129","timestamp":"2025-06-15 02:23:43"}
{"level":"info","message":"Found active discount for 01200426532: PERCENTAGE with value 25","timestamp":"2025-06-15 02:23:43"}
{"level":"info","message":"PERCENTAGE: 129 - 25% = 96.75","timestamp":"2025-06-15 02:23:43"}
{"level":"info","message":"Discount applied: 01200426532 - Original: 129, Final: 96.75, Discount %: 25","timestamp":"2025-06-15 02:23:43"}
{"level":"info","message":"Price changed for 01200426532: 129 -> 96.75","timestamp":"2025-06-15 02:23:43"}
{"level":"info","message":"Updated product 01200426532 with new price 96.75","timestamp":"2025-06-15 02:23:43"}
{"level":"info","message":"Created price history record for 01200426532","timestamp":"2025-06-15 02:23:43"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-15 02:23:43"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbuqdkl00003hxv4vm6utec5 updated <NAME_EMAIL>","timestamp":"2025-06-15 02:23:43"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbuoiyb205lt9whxhklceafb (12317501755)","timestamp":"2025-06-15 02:23:53"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiyb205lt9whxhklceafb","timestamp":"2025-06-15 02:23:53"}
{"level":"info","message":"Found product 12317501755 with base price 987","timestamp":"2025-06-15 02:23:53"}
{"level":"info","message":"Found active discount for 12317501755: PERCENTAGE with value 25","timestamp":"2025-06-15 02:23:53"}
{"level":"info","message":"PERCENTAGE: 987 - 25% = 740.25","timestamp":"2025-06-15 02:23:53"}
{"level":"info","message":"Discount applied: 12317501755 - Original: 987, Final: 740.25, Discount %: 25","timestamp":"2025-06-15 02:23:53"}
{"level":"info","message":"Price changed for 12317501755: 987 -> 740.25","timestamp":"2025-06-15 02:23:53"}
{"level":"info","message":"Updated product 12317501755 with new price 740.25","timestamp":"2025-06-15 02:23:53"}
{"level":"info","message":"Created price history record for 12317501755","timestamp":"2025-06-15 02:23:53"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-15 02:23:53"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbuqdkl00003hxv4vm6utec5 updated <NAME_EMAIL>","timestamp":"2025-06-15 02:23:53"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiy9f05k79whxbc5eh2gg","timestamp":"2025-06-15 02:35:28"}
{"level":"info","message":"Found product 11531743192 with base price 1000","timestamp":"2025-06-15 02:35:28"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-15 02:35:28"}
{"level":"info","message":"Price changed for 11531743192: 750 -> 1000","timestamp":"2025-06-15 02:35:28"}
{"level":"info","message":"Updated product 11531743192 with new price 1000","timestamp":"2025-06-15 02:35:28"}
{"level":"info","message":"Created price history record for 11531743192","timestamp":"2025-06-15 02:35:28"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbuoiy9f05k79whxbc5eh2gg for the discountId cmbuqdkl00003hxv4vm6utec5 by <EMAIL>","timestamp":"2025-06-15 02:35:28"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiucn004r9whxa4osg2qv","timestamp":"2025-06-15 02:36:02"}
{"level":"info","message":"Found product 11000435439 with base price 500","timestamp":"2025-06-15 02:36:02"}
{"level":"info","message":"No active discount found for product 11000435439","timestamp":"2025-06-15 02:36:02"}
{"level":"info","message":"Price changed for 11000435439: 375 -> 500","timestamp":"2025-06-15 02:36:02"}
{"level":"info","message":"Updated product 11000435439 with new price 500","timestamp":"2025-06-15 02:36:02"}
{"level":"info","message":"Created price history record for 11000435439","timestamp":"2025-06-15 02:36:02"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbuoiucn004r9whxa4osg2qv for the discountId cmbuqdkl00003hxv4vm6utec5 by <EMAIL>","timestamp":"2025-06-15 02:36:02"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiyb205lt9whxhklceafb","timestamp":"2025-06-15 02:37:38"}
{"level":"info","message":"Found product 12317501755 with base price 100","timestamp":"2025-06-15 02:37:38"}
{"level":"info","message":"No active discount found for product 12317501755","timestamp":"2025-06-15 02:37:38"}
{"level":"info","message":"Price changed for 12317501755: 75 -> 100","timestamp":"2025-06-15 02:37:38"}
{"level":"info","message":"Updated product 12317501755 with new price 100","timestamp":"2025-06-15 02:37:38"}
{"level":"info","message":"Created price history record for 12317501755","timestamp":"2025-06-15 02:37:38"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbuoiyb205lt9whxhklceafb for the discountId cmbuqdkl00003hxv4vm6utec5 by <EMAIL>","timestamp":"2025-06-15 02:37:38"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiuaj00069whx25ashkot","timestamp":"2025-06-15 02:39:24"}
{"level":"info","message":"Found product 01200426532 with base price 200","timestamp":"2025-06-15 02:39:24"}
{"level":"info","message":"No active discount found for product 01200426532","timestamp":"2025-06-15 02:39:24"}
{"level":"info","message":"Price changed for 01200426532: 150 -> 200","timestamp":"2025-06-15 02:39:24"}
{"level":"info","message":"Updated product 01200426532 with new price 200","timestamp":"2025-06-15 02:39:24"}
{"level":"info","message":"Created price history record for 01200426532","timestamp":"2025-06-15 02:39:24"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbuoiuaj00069whx25ashkot for the discountId cmbuqdkl00003hxv4vm6utec5 by <EMAIL>","timestamp":"2025-06-15 02:39:24"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbuoiy9f05k79whxbc5eh2gg (11531743192)","timestamp":"2025-06-15 02:40:26"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiy9f05k79whxbc5eh2gg","timestamp":"2025-06-15 02:40:26"}
{"level":"info","message":"Found product 11531743192 with base price 1000","timestamp":"2025-06-15 02:40:26"}
{"level":"info","message":"Found active discount for 11531743192: PERCENTAGE with value 25","timestamp":"2025-06-15 02:40:26"}
{"level":"info","message":"PERCENTAGE: 1000 - 25% = 750","timestamp":"2025-06-15 02:40:26"}
{"level":"info","message":"Discount applied: 11531743192 - Original: 1000, Final: 750, Discount %: 25","timestamp":"2025-06-15 02:40:26"}
{"level":"info","message":"Price changed for 11531743192: 1000 -> 750","timestamp":"2025-06-15 02:40:26"}
{"level":"info","message":"Updated product 11531743192 with new price 750","timestamp":"2025-06-15 02:40:26"}
{"level":"info","message":"Created price history record for 11531743192","timestamp":"2025-06-15 02:40:26"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-15 02:40:26"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbuqdkl00003hxv4vm6utec5 updated <NAME_EMAIL>","timestamp":"2025-06-15 02:40:26"}
{"level":"info","message":"Starting expired discount check at 2025-06-14T23:40:36.772Z","timestamp":"2025-06-15 02:40:36"}
{"level":"info","message":"Found 0 expired discounts to process","timestamp":"2025-06-15 02:40:36"}
{"level":"info","message":"Starting expired discount check at 2025-06-14T23:40:37.129Z","timestamp":"2025-06-15 02:40:37"}
{"level":"info","message":"Found 0 expired discounts to process","timestamp":"2025-06-15 02:40:37"}
{"level":"info","message":"Starting expired discount check at 2025-06-14T23:40:39.147Z","timestamp":"2025-06-15 02:40:39"}
{"level":"info","message":"Found 0 expired discounts to process","timestamp":"2025-06-15 02:40:39"}
{"level":"info","message":"Starting expired discount check at 2025-06-14T23:40:50.629Z","timestamp":"2025-06-15 02:40:50"}
{"level":"info","message":"Found 0 expired discounts to process","timestamp":"2025-06-15 02:40:50"}
{"level":"info","message":"Starting expired discount check at 2025-06-14T23:40:51.331Z","timestamp":"2025-06-15 02:40:51"}
{"level":"info","message":"Found 0 expired discounts to process","timestamp":"2025-06-15 02:40:51"}
{"level":"info","message":"Starting expired discount check at 2025-06-14T23:41:07.962Z","timestamp":"2025-06-15 02:41:07"}
{"level":"info","message":"Found 0 expired discounts to process","timestamp":"2025-06-15 02:41:07"}
{"level":"info","message":"Starting expired discount check at 2025-06-14T23:41:08.549Z","timestamp":"2025-06-15 02:41:08"}
{"level":"info","message":"Found 0 expired discounts to process","timestamp":"2025-06-15 02:41:08"}
{"level":"info","message":"Discount with name Summer tyres updated <NAME_EMAIL>","timestamp":"2025-06-15 02:41:55"}
{"level":"info","message":"Starting expired discount check at 2025-06-14T23:42:13.788Z","timestamp":"2025-06-15 02:42:13"}
{"level":"info","message":"Found 1 expired discounts to process","timestamp":"2025-06-15 02:42:13"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiy9f05k79whxbc5eh2gg","timestamp":"2025-06-15 02:42:13"}
{"level":"info","message":"Found product 11531743192 with base price 1000","timestamp":"2025-06-15 02:42:13"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-15 02:42:13"}
{"level":"info","message":"Price changed for 11531743192: 750 -> 1000","timestamp":"2025-06-15 02:42:13"}
{"level":"info","message":"Updated product 11531743192 with new price 1000","timestamp":"2025-06-15 02:42:14"}
{"level":"info","message":"Created price history record for 11531743192","timestamp":"2025-06-15 02:42:14"}
{"level":"info","message":"Processed expired discount cmbuqdkl00003hxv4vm6utec5: deactivated and removed 1 products","timestamp":"2025-06-15 02:42:14"}
{"level":"info","message":"Starting expired discount check at 2025-06-14T23:42:15.357Z","timestamp":"2025-06-15 02:42:15"}
{"level":"info","message":"Found 0 expired discounts to process","timestamp":"2025-06-15 02:42:15"}
{"level":"info","message":"Starting expired discount check at 2025-06-14T23:42:15.885Z","timestamp":"2025-06-15 02:42:15"}
{"level":"info","message":"Found 0 expired discounts to process","timestamp":"2025-06-15 02:42:15"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbuoiy9f05k79whxbc5eh2gg (11531743192)","timestamp":"2025-06-15 02:42:55"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiy9f05k79whxbc5eh2gg","timestamp":"2025-06-15 02:42:55"}
{"level":"info","message":"Found product 11531743192 with base price 1000","timestamp":"2025-06-15 02:42:55"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-15 02:42:55"}
{"level":"info","message":"No price change for 11531743192: 1000","timestamp":"2025-06-15 02:42:55"}
{"level":"info","message":"Updated product 11531743192 with new price 1000","timestamp":"2025-06-15 02:42:55"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-15 02:42:55"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbuqdkl00003hxv4vm6utec5 updated <NAME_EMAIL>","timestamp":"2025-06-15 02:42:55"}
{"level":"info","message":"Discount with name Summer tyres updated <NAME_EMAIL>","timestamp":"2025-06-15 02:43:18"}
{"level":"info","message":"Starting expired discount check at 2025-06-14T23:43:24.179Z","timestamp":"2025-06-15 02:43:24"}
{"level":"info","message":"Found 0 expired discounts to process","timestamp":"2025-06-15 02:43:24"}
{"level":"info","message":"Starting expired discount check at 2025-06-14T23:43:26.309Z","timestamp":"2025-06-15 02:43:26"}
{"level":"info","message":"Found 0 expired discounts to process","timestamp":"2025-06-15 02:43:26"}
{"level":"info","message":"Starting expired discount check at 2025-06-14T23:43:28.174Z","timestamp":"2025-06-15 02:43:28"}
{"level":"info","message":"Found 0 expired discounts to process","timestamp":"2025-06-15 02:43:28"}
{"level":"info","message":"Discount with name Summer tyres updated <NAME_EMAIL>","timestamp":"2025-06-15 02:43:44"}
{"level":"info","message":"Starting expired discount check at 2025-06-14T23:43:55.401Z","timestamp":"2025-06-15 02:43:55"}
{"level":"info","message":"Found 0 expired discounts to process","timestamp":"2025-06-15 02:43:55"}
{"level":"info","message":"Starting expired discount check at 2025-06-14T23:43:56.178Z","timestamp":"2025-06-15 02:43:56"}
{"level":"info","message":"Found 0 expired discounts to process","timestamp":"2025-06-15 02:43:56"}
{"level":"info","message":"Starting expired discount check at 2025-06-14T23:43:56.773Z","timestamp":"2025-06-15 02:43:56"}
{"level":"info","message":"Found 0 expired discounts to process","timestamp":"2025-06-15 02:43:56"}
{"level":"info","message":"Discount with name Summer tyres updated <NAME_EMAIL>","timestamp":"2025-06-15 02:46:27"}
{"level":"error","message":"DiscountAddProductAction -> Product \"11531743192\" already has a <NAME_EMAIL>","timestamp":"2025-06-15 02:47:39"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbuoiyb205lt9whxhklceafb (12317501755)","timestamp":"2025-06-15 02:47:45"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiyb205lt9whxhklceafb","timestamp":"2025-06-15 02:47:45"}
{"level":"info","message":"Found product 12317501755 with base price 100","timestamp":"2025-06-15 02:47:45"}
{"level":"info","message":"Found active discount for 12317501755: PERCENTAGE with value 25","timestamp":"2025-06-15 02:47:45"}
{"level":"info","message":"PERCENTAGE: 100 - 25% = 75","timestamp":"2025-06-15 02:47:45"}
{"level":"info","message":"Discount applied: 12317501755 - Original: 100, Final: 75, Discount %: 25","timestamp":"2025-06-15 02:47:45"}
{"level":"info","message":"Price changed for 12317501755: 100 -> 75","timestamp":"2025-06-15 02:47:45"}
{"level":"info","message":"Updated product 12317501755 with new price 75","timestamp":"2025-06-15 02:47:45"}
{"level":"info","message":"Created price history record for 12317501755","timestamp":"2025-06-15 02:47:45"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-15 02:47:45"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbuqdkl00003hxv4vm6utec5 updated <NAME_EMAIL>","timestamp":"2025-06-15 02:47:45"}
{"level":"info","message":"Starting expired discount check at 2025-06-14T23:48:52.485Z","timestamp":"2025-06-15 02:48:52"}
{"level":"info","message":"Found 1 expired discounts to process","timestamp":"2025-06-15 02:48:52"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiy9f05k79whxbc5eh2gg","timestamp":"2025-06-15 02:48:52"}
{"level":"info","message":"Found product 11531743192 with base price 1000","timestamp":"2025-06-15 02:48:52"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-15 02:48:52"}
{"level":"info","message":"No price change for 11531743192: 1000","timestamp":"2025-06-15 02:48:52"}
{"level":"info","message":"Updated product 11531743192 with new price 1000","timestamp":"2025-06-15 02:48:52"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiyb205lt9whxhklceafb","timestamp":"2025-06-15 02:48:52"}
{"level":"info","message":"Found product 12317501755 with base price 100","timestamp":"2025-06-15 02:48:52"}
{"level":"info","message":"No active discount found for product 12317501755","timestamp":"2025-06-15 02:48:52"}
{"level":"info","message":"Price changed for 12317501755: 75 -> 100","timestamp":"2025-06-15 02:48:52"}
{"level":"info","message":"Updated product 12317501755 with new price 100","timestamp":"2025-06-15 02:48:52"}
{"level":"info","message":"Created price history record for 12317501755","timestamp":"2025-06-15 02:48:52"}
{"level":"info","message":"Processed expired discount cmbuqdkl00003hxv4vm6utec5: deactivated and removed 2 products","timestamp":"2025-06-15 02:48:52"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbuoiy9f05k79whxbc5eh2gg (11531743192)","timestamp":"2025-06-15 02:59:33"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiy9f05k79whxbc5eh2gg","timestamp":"2025-06-15 02:59:33"}
{"level":"info","message":"Found product 11531743192 with base price 1000","timestamp":"2025-06-15 02:59:33"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-15 02:59:33"}
{"level":"info","message":"No price change for 11531743192: 1000","timestamp":"2025-06-15 02:59:33"}
{"level":"info","message":"Updated product 11531743192 with new price 1000","timestamp":"2025-06-15 02:59:33"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-15 02:59:33"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbuqdkl00003hxv4vm6utec5 updated <NAME_EMAIL>","timestamp":"2025-06-15 02:59:33"}
{"level":"info","message":"Discount with name Summer tyres updated <NAME_EMAIL>","timestamp":"2025-06-15 03:00:07"}
{"level":"info","message":"Discount with name Summer tyres updated <NAME_EMAIL>","timestamp":"2025-06-15 03:00:15"}
{"level":"info","message":"Discount with name Summer tyres updated <NAME_EMAIL>","timestamp":"2025-06-15 03:00:24"}
{"level":"info","message":"Starting expired discount check at 2025-06-15T00:00:32.947Z","timestamp":"2025-06-15 03:00:32"}
{"level":"info","message":"Found 0 expired discounts to process","timestamp":"2025-06-15 03:00:32"}
{"level":"info","message":"Starting activate discount check at 2025-06-15T00:00:39.962Z","timestamp":"2025-06-15 03:00:39"}
{"level":"info","message":"Found 1 discounts to activate","timestamp":"2025-06-15 03:00:39"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiy9f05k79whxbc5eh2gg","timestamp":"2025-06-15 03:00:40"}
{"level":"info","message":"Found product 11531743192 with base price 1000","timestamp":"2025-06-15 03:00:40"}
{"level":"info","message":"Found active discount for 11531743192: PERCENTAGE with value 25","timestamp":"2025-06-15 03:00:40"}
{"level":"info","message":"PERCENTAGE: 1000 - 25% = 750","timestamp":"2025-06-15 03:00:40"}
{"level":"info","message":"Discount applied: 11531743192 - Original: 1000, Final: 750, Discount %: 25","timestamp":"2025-06-15 03:00:40"}
{"level":"info","message":"Price changed for 11531743192: 1000 -> 750","timestamp":"2025-06-15 03:00:40"}
{"level":"info","message":"Updated product 11531743192 with new price 750","timestamp":"2025-06-15 03:00:40"}
{"level":"info","message":"Created price history record for 11531743192","timestamp":"2025-06-15 03:00:40"}
{"level":"info","message":"Activated discount cmbuqdkl00003hxv4vm6utec5: activated and updated 1 products","timestamp":"2025-06-15 03:00:40"}
{"level":"info","message":"Starting activate discount check at 2025-06-15T00:00:41.737Z","timestamp":"2025-06-15 03:00:41"}
{"level":"info","message":"Found 0 discounts to activate","timestamp":"2025-06-15 03:00:41"}
{"level":"info","message":"No discounts found that need activation","timestamp":"2025-06-15 03:00:41"}
{"level":"info","message":"Starting expired discount check at 2025-06-15T00:01:25.296Z","timestamp":"2025-06-15 03:01:25"}
{"level":"info","message":"Found 1 expired discounts to process","timestamp":"2025-06-15 03:01:25"}
{"level":"info","message":"Starting updateProductPrice for product cmbuoiy9f05k79whxbc5eh2gg","timestamp":"2025-06-15 03:01:25"}
{"level":"info","message":"Found product 11531743192 with base price 1000","timestamp":"2025-06-15 03:01:25"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-06-15 03:01:25"}
{"level":"info","message":"Price changed for 11531743192: 750 -> 1000","timestamp":"2025-06-15 03:01:25"}
{"level":"info","message":"Updated product 11531743192 with new price 1000","timestamp":"2025-06-15 03:01:25"}
{"level":"info","message":"Created price history record for 11531743192","timestamp":"2025-06-15 03:01:25"}
{"level":"info","message":"Processed expired discount cmbuqdkl00003hxv4vm6utec5: deactivated and removed 1 products","timestamp":"2025-06-15 03:01:25"}
