2025-05-16 15:03:15 [INFO]: Discount with name xxx created successfully
2025-05-16 15:03:27 [ERROR]: createDiscountAction -> A discount with this name already exists: xxx
2025-05-16 15:04:43 [ERROR]: updateDiscountAction -> The discount allready exists: : xxxhgdhdhd
2025-05-16 15:04:43 [INFO]: Discount with name xxxhgdhdhd updated successfully
2025-05-16 15:05:15 [ERROR]: updateDiscountAction -> The discount allready exists: : xxx
2025-05-16 15:05:15 [INFO]: Discount with name xxx updated successfully
2025-05-16 15:05:31 [ERROR]: updateDiscountAction -> The discount allready exists: : xxx
2025-05-16 15:22:25 [INFO]: Discount with name xxx updated successfully
2025-05-16 15:22:34 [ERROR]: updateDiscountAction -> The discount allready exists: : xxx
2025-05-16 15:22:53 [ERROR]: updateDiscountAction -> The discount allready exists: : xxx
2025-05-16 15:22:56 [ERROR]: updateDiscountAction -> The discount allready exists: : xxxf
2025-05-16 15:22:56 [INFO]: Discount with name xxxf updated successfully
2025-05-16 15:23:26 [INFO]: Discount with name xxxf updated successfully
2025-05-16 15:27:31 [INFO]: Discount with name xxx updated successfully
2025-05-16 15:28:26 [INFO]: Discount with name adfsdf created successfully
2025-05-16 15:33:20 [ERROR]: updateDiscountAction -> The discount allready exists: : xxx
2025-05-16 16:08:02 [ERROR]: updateDiscountAction -> The discount allready exists: : xxx
2025-05-16 16:08:59 [INFO]: discountAddProductAction -> Discount with id 30 updated successfully
2025-05-16 16:09:09 [ERROR]: discountAddProductAction -> Product 01090398983 already has an active discount.: undefined
2025-05-16 16:11:54 [ERROR]: createDiscountAction -> A discount with this name already exists: xxx
2025-05-16 16:12:02 [INFO]: Discount with name jghhjhgj created successfully
2025-05-16 16:26:06 [ERROR]: discountAddProductAction -> Product 11127502650 already has an active discount.: undefined
2025-05-16 16:26:28 [ERROR]: discountAddProductAction -> Product 01090398983 already has an active discount.: undefined
2025-05-16 16:26:35 [INFO]: discountAddProductAction -> Discount with id 31 updated successfully
2025-05-16 16:26:42 [INFO]: discountAddProductAction -> Discount with id 31 updated successfully
2025-05-16 16:27:56 [INFO]: discountAddProductAction -> Discount with id 31 updated successfully
2025-05-16 16:28:05 [ERROR]: discountAddProductAction -> Product 01410012466 already has an active discount.: undefined
2025-05-16 16:28:12 [ERROR]: discountAddProductAction -> Product 01090398983 already has an active discount.: undefined
2025-05-16 17:07:01 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      0
    ]
  }
]
2025-05-16 17:07:09 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      0
    ]
  }
]
2025-05-16 17:07:33 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      0
    ]
  }
]
2025-05-16 17:08:10 [ERROR]: processCSVDiscountProductAction -> No products found in DB for the provided material numbers: undefined
2025-05-16 17:08:42 [INFO]: processCSVDiscountProductAction -> success for the discount 31 and with the products: [object Object]
2025-05-16 17:08:48 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01410012466
2025-05-16 17:09:17 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01410012466
2025-05-16 17:09:25 [ERROR]: discountAddProductAction -> Product 01410012466 already has an active discount.: undefined
2025-05-16 17:09:29 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01410012466
2025-05-16 17:09:55 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01090398983,01410012466
2025-05-17 08:37:10 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01090398983,01410012466
2025-05-17 08:48:09 [ERROR]: processCSVDiscountProductAction -> No products found in DB for the provided material numbers: undefined
2025-05-17 09:20:01 [ERROR]: processCSVDiscountProductAction -> No material numbers provided: undefined
2025-05-17 09:22:24 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      0
    ]
  }
]
2025-05-17 09:22:52 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      0
    ]
  }
]
2025-05-17 09:23:35 [ERROR]: processCSVDiscountProductAction -> Missing products: 1.23123E+11: undefined
2025-05-17 09:23:53 [ERROR]: processCSVDiscountProductAction -> Missing products: 1.23123E+11: undefined
2025-05-17 09:24:20 [ERROR]: processCSVDiscountProductAction -> Missing products: 1.23123E+11: undefined
2025-05-17 09:24:57 [INFO]: processCSVDiscountProductAction -> success for the discount 30 and with the products: [object Object]
2025-05-17 09:26:32 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01200426532
2025-05-17 09:27:03 [INFO]: processCSVDiscountProductAction -> success for the discount 30 and with the products: [object Object]
2025-05-17 09:28:06 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01200426532
2025-05-17 09:28:15 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01200426532
2025-05-17 09:34:27 [INFO]: processCSVDiscountProductAction -> success for the discount 30 and with the products: [object Object]
2025-05-17 09:35:29 [ERROR]: processCSVDiscountProductAction -> Missing products: 9999999991h: undefined
2025-05-17 09:35:57 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      2
    ]
  }
]
2025-05-17 09:36:57 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      2
    ]
  }
]
2025-05-17 09:37:27 [ERROR]: processCSVDiscountProductAction -> Missing products: 9999999991h, 123123tgbhy: undefined
2025-05-17 09:39:29 [ERROR]: processCSVDiscountProductAction -> Missing products: 9999999991h, 123123tgbhy: undefined
2025-05-17 09:39:34 [ERROR]: processCSVDiscountProductAction -> Missing products: 9999999991h, 123123tgbhy: undefined
2025-05-17 09:39:52 [ERROR]: processCSVDiscountProductAction -> Missing products: 9999999991h, 123123tgbhy: undefined
2025-05-17 09:39:56 [ERROR]: processCSVDiscountProductAction -> Missing products: 9999999991h, 123123tgbhy: undefined
2025-05-17 22:54:16 [ERROR]: processCSVDiscountProductAction -> Missing products: 9999999991h, 123123tgbhy: undefined
2025-05-17 22:55:17 [ERROR]: processCSVDiscountProductAction -> Missing products: 9999999991h, 123123tgbhy: undefined
2025-05-17 22:55:48 [ERROR]: processCSVDiscountProductAction -> Missing products: 123123tgbhy: undefined
2025-05-17 23:08:06 [ERROR]: processCSVDiscountProductAction -> Missing products: 123123tgbhy: undefined
2025-05-17 23:08:41 [ERROR]: processCSVDiscountProductAction -> Missing products: 123ERTGB56Y: undefined
2025-05-17 23:08:48 [ERROR]: processCSVDiscountProductAction -> Missing products: 123ERTGB56Y: undefined
2025-05-17 23:08:54 [ERROR]: processCSVDiscountProductAction -> Missing products: 123ERTGB56Y: undefined
2025-05-17 23:09:02 [ERROR]: processCSVDiscountProductAction -> Missing products: 123ERTGB56Y: undefined
2025-05-17 23:09:10 [ERROR]: processCSVDiscountProductAction -> Missing products: 123ERTGB56Y: undefined
2025-05-17 23:12:03 [ERROR]: processCSVDiscountProductAction -> Missing products: 123ERTGB56Y: undefined
2025-05-17 23:14:15 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01090398983
2025-05-17 23:14:26 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01090398983
2025-05-17 23:14:58 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01090398983
2025-05-17 23:29:10 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01090398983
2025-05-17 23:29:25 [INFO]: processCSVDiscountProductAction -> success for the discount 30 and with the products: [object Object],[object Object],[object Object]
2025-05-17 23:30:07 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      2
    ]
  }
]
2025-05-17 23:31:24 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      2
    ]
  }
]
2025-05-17 23:55:24 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      2
    ]
  }
]
2025-05-17 23:55:42 [INFO]: processCSVDiscountProductAction -> success for the discount 30 and with the products: [object Object],[object Object],[object Object]
2025-05-17 23:56:36 [ERROR]: discountAddProductAction -> Product 01090398983 already has an active discount.: undefined
2025-05-17 23:57:15 [ERROR]: discountAddProductAction -> Product "0109039898x" not found.: undefined
2025-05-17 23:57:41 [ERROR]: discountAddProductAction -> Product "ff09039898x" not found.: undefined
2025-05-18 00:00:41 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      2
    ]
  }
]
2025-05-18 00:00:45 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      2
    ]
  }
]
2025-05-18 00:02:16 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01090398983,01200426532,64115A1BDB6
2025-05-18 00:07:33 [ERROR]: discountAddProductAction -> Product 01200426528 already has an active discount.: undefined
2025-05-18 00:07:47 [INFO]: deleteProductFromDiscountAction -> deleted with success the productId 104836 for the discountId 30
2025-05-18 00:07:50 [INFO]: deleteProductFromDiscountAction -> deleted with success the productId 104830 for the discountId 30
2025-05-18 00:37:11 [INFO]: deleteProductFromDiscountAction -> deleted with success the productId 104900 for the discountId 31
2025-05-18 00:38:00 [ERROR]: discountAddProductAction -> Product 11127502650 already has an active discount.: undefined
2025-05-18 00:38:08 [INFO]: discountAddProductAction -> Discount with id 31 updated successfully
2025-05-18 00:39:21 [INFO]: deleteProductFromDiscountAction -> deleted with success the productId 104830 for the discountId 31
2025-05-18 00:39:42 [ERROR]: discountAddProductAction -> Product 01200426528 already has an active discount.: undefined
2025-05-18 00:39:48 [INFO]: discountAddProductAction -> Discount with id 25 updated successfully
2025-05-19 09:04:28 [ERROR]: discountAddProductAction -> Product 01090398983 already has an active discount.: undefined
2025-05-19 09:04:33 [ERROR]: discountAddProductAction -> Product 01200426528 already has an active discount.: undefined
2025-05-19 09:04:38 [INFO]: discountAddProductAction -> Discount with id 31 updated successfully
2025-05-19 09:05:15 [INFO]: deleteProductFromDiscountAction -> deleted with success the productId 104900 for the discountId 31
2025-05-19 09:41:07 [INFO]: discountAddProductAction -> Discount with id 31 updated successfully
2025-05-19 10:17:08 [INFO]: deleteAllProductsFromDiscount -> deleted with success 1 products from the discountId 31
2025-05-19 10:17:16 [ERROR]: discountAddProductAction -> Database error: : 
Invalid `prisma.productDiscount.create()` invocation:


Unique constraint failed on the fields: (`productId`)
2025-05-19 10:17:21 [ERROR]: discountAddProductAction -> Database error: : 
Invalid `prisma.productDiscount.create()` invocation:


Unique constraint failed on the fields: (`productId`)
2025-05-19 10:17:25 [INFO]: discountAddProductAction -> Discount with id 31 updated successfully
2025-05-19 10:17:32 [ERROR]: discountAddProductAction -> Database error: : 
Invalid `prisma.productDiscount.create()` invocation:


Unique constraint failed on the fields: (`productId`)
2025-05-19 10:17:43 [ERROR]: discountAddProductAction -> Database error: : 
Invalid `prisma.productDiscount.create()` invocation:


Unique constraint failed on the fields: (`productId`)
2025-05-19 10:24:57 [ERROR]: discountAddProductAction -> Product "01200411861" not found.: undefined
2025-05-19 10:25:05 [ERROR]: discountAddProductAction -> Database error: : 
Invalid `prisma.productDiscount.create()` invocation:


Unique constraint failed on the fields: (`productId`)
2025-05-19 10:30:43 [ERROR]: discountAddProductAction -> Database error: : 
Invalid `prisma.productDiscount.create()` invocation:


Unique constraint failed on the fields: (`productId`)
2025-05-19 10:31:07 [ERROR]: discountAddProductAction -> Database error: : 
Invalid `prisma.productDiscount.create()` invocation:


Unique constraint failed on the fields: (`productId`)
2025-05-19 10:34:23 [ERROR]: discountAddProductAction -> Product 01090398983 already has an active discount.: undefined
2025-05-19 10:34:33 [ERROR]: discountAddProductAction -> Product 01090398983 already has an active discount.: undefined
2025-05-19 10:34:38 [ERROR]: discountAddProductAction -> Database error: : 
Invalid `prisma.productDiscount.create()` invocation:


Unique constraint failed on the fields: (`productId`)
2025-05-19 10:35:13 [ERROR]: discountAddProductAction -> Product 01090398983 already has an active discount.: undefined
2025-05-19 10:36:44 [ERROR]: discountAddProductAction -> Product 11127502650 already has an active discount.: undefined
2025-05-19 10:36:48 [ERROR]: discountAddProductAction -> Product 01200426528 already has an active discount.: undefined
2025-05-19 10:36:53 [ERROR]: discountAddProductAction -> Product 01410012466 already has an active discount.: undefined
2025-05-19 10:36:57 [ERROR]: discountAddProductAction -> Database error: : 
Invalid `prisma.productDiscount.create()` invocation:


Unique constraint failed on the fields: (`productId`)
2025-05-19 10:39:13 [ERROR]: discountAddProductAction -> Database error: : 
Invalid `prisma.productDiscount.create()` invocation:


Unique constraint failed on the fields: (`productId`)
2025-05-19 10:40:54 [ERROR]: discountAddProductAction -> Product "12364564764" not found.: undefined
2025-05-19 10:41:05 [INFO]: deleteAllProductsFromDiscount -> deleted with success 1 products from the discountId 31
2025-05-19 10:51:38 [INFO]: discountAddProductAction -> Discount with id 31 updated successfully
2025-05-19 10:56:47 [INFO]: deleteAllProductsFromDiscount -> deleted with success 1 products from the discountId 31
2025-05-19 10:57:14 [ERROR]: discountAddProductAction -> Product 01200426528 already has an active discount.: undefined
2025-05-19 10:57:19 [INFO]: discountAddProductAction -> Discount with id 31 updated successfully
2025-05-19 10:57:25 [INFO]: deleteAllProductsFromDiscount -> deleted with success 1 products from the discountId 31
2025-05-19 10:59:00 [INFO]: discountAddProductAction -> Discount with id 31 updated successfully
2025-05-19 10:59:08 [INFO]: deleteAllProductsFromDiscount -> deleted with success 1 products from the discountId 31
2025-05-19 11:00:25 [INFO]: discountAddProductAction -> Discount with id 31 updated successfully
2025-05-19 12:15:03 [ERROR]: DiscountAddProductAction -> Database error: PrismaClientKnownRequestError: 
Invalid `prisma.productDiscount.create()` invocation:


Unique constraint failed on the fields: (`productId`)  by cosmin.oprea: undefined
2025-05-19 12:15:10 [ERROR]: DiscountAddProductAction -> Product 01200426528 already has an active discount by cosmin.oprea: undefined
2025-05-19 12:15:14 [ERROR]: DiscountAddProductAction -> Product 11127502650 already has an active discount by cosmin.oprea: undefined
2025-05-19 12:15:20 [ERROR]: DiscountAddProductAction -> Product "13423424234" not found by cosmin.oprea: undefined
2025-05-19 14:44:18 [INFO]: AttributesSearchProductAction -> Search for 01090398983 with success by cosmin.oprea
2025-05-19 16:11:28 [INFO]: ProductSearchAction -> Search for 01090398983 with success by cosmin.oprea
2025-05-19 16:12:35 [INFO]: ProductSearchAction -> Search for 01090398983 with success by cosmin.oprea
2025-05-19 16:13:16 [INFO]: ProductSearchAction -> Search for 01090398983 with success by cosmin.oprea
2025-05-19 16:20:13 [INFO]: ProductSearchAction -> Search for 01090398983 with success by cosmin.oprea
2025-05-19 16:20:23 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-19 16:20:26 [INFO]: ProductSearchAction -> Search for 01200426528 with success by cosmin.oprea
2025-05-19 16:20:28 [INFO]: ProductSearchAction -> Search for 01200411868 with success by cosmin.oprea
2025-05-19 16:20:53 [INFO]: ProductSearchAction -> Search for 01099760404 with success by cosmin.oprea
2025-05-19 16:21:20 [INFO]: ProductSearchAction -> Search for 01099760404 with success by cosmin.oprea
2025-05-19 16:22:21 [INFO]: ProductSearchAction -> Search for 01090398983 with success by cosmin.oprea
2025-05-19 16:27:54 [ERROR]: ProductSearchAction -> Product "11134241231" not found by cosmin.oprea: undefined
2025-05-19 16:28:00 [ERROR]: ProductSearchAction -> Product "11134241231" not found by cosmin.oprea: undefined
2025-05-19 16:28:07 [ERROR]: ProductSearchAction -> Product "11134241231" not found by cosmin.oprea: undefined
2025-05-19 16:33:04 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-19 16:47:02 [INFO]: ProductSearchAction -> Search for 01410012466 with success by cosmin.oprea
2025-05-19 16:48:55 [INFO]: ProductSearchAction -> Search for 01090398983 with success by cosmin.oprea
2025-05-19 16:52:57 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-19 18:41:04 [INFO]: ProductSearchAction -> Search for 01090398983 with success by cosmin.oprea
2025-05-19 18:41:24 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-19 18:42:48 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-19 18:45:30 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-19 19:00:29 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-19 19:50:20 [INFO]: ProductSearchAction -> Search for 01090398983 with success by cosmin.oprea
2025-05-19 19:51:46 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 08:22:37 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 08:31:22 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 08:32:13 [INFO]: ProductSearchAction -> Search for 01090398983 with success by cosmin.oprea
2025-05-21 08:32:16 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 08:45:05 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 08:54:35 [INFO]: ProductSearchAction -> Search for 01090398983 with success by cosmin.oprea
2025-05-21 08:54:37 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 08:54:47 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 08:54:57 [ERROR]: ProductSearchAction -> Product "12312312312" not found by cosmin.oprea: undefined
2025-05-21 08:55:05 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 09:02:25 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 09:18:35 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 09:24:12 [INFO]: ProductSearchAction -> Search for 01410012466 with success by cosmin.oprea
2025-05-21 09:24:15 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 09:58:11 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 10:23:48 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 10:54:20 [INFO]: ProductSearchAction -> Search for 01090398983 with success by cosmin.oprea
2025-05-21 10:54:24 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 11:01:48 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 11:02:30 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 11:04:22 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 11:51:08 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "undefined",
    "path": [],
    "message": "Required"
  }
] by cosmin.oprea: undefined
2025-05-21 11:53:07 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "undefined",
    "path": [],
    "message": "Required"
  }
] by cosmin.oprea: undefined
2025-05-21 11:53:55 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "undefined",
    "path": [],
    "message": "Required"
  }
] by cosmin.oprea: undefined
2025-05-21 11:55:18 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "undefined",
    "path": [],
    "message": "Required"
  }
] by cosmin.oprea: undefined
2025-05-21 11:56:26 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "undefined",
    "path": [],
    "message": "Required"
  }
] by cosmin.oprea: undefined
2025-05-21 11:58:04 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "undefined",
    "path": [],
    "message": "Required"
  }
] by cosmin.oprea: undefined
2025-05-21 11:58:19 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "undefined",
    "path": [],
    "message": "Required"
  }
] by cosmin.oprea: undefined
2025-05-21 12:00:25 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "undefined",
    "path": [],
    "message": "Required"
  }
] by cosmin.oprea: undefined
2025-05-21 12:13:15 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "undefined",
    "path": [],
    "message": "Required"
  }
] by cosmin.oprea: undefined
2025-05-21 12:15:06 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "string",
    "path": [],
    "message": "Expected object, received string"
  }
] by cosmin.oprea: undefined
2025-05-21 12:15:12 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "string",
    "path": [],
    "message": "Expected object, received string"
  }
] by cosmin.oprea: undefined
2025-05-21 12:15:25 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "string",
    "path": [],
    "message": "Expected object, received string"
  }
] by cosmin.oprea: undefined
2025-05-21 12:15:34 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "string",
    "path": [],
    "message": "Expected object, received string"
  }
] by cosmin.oprea: undefined
2025-05-21 12:16:37 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "string",
    "path": [],
    "message": "Expected object, received string"
  }
] by cosmin.oprea: undefined
2025-05-21 12:17:10 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "string",
    "path": [],
    "message": "Expected object, received string"
  }
] by cosmin.oprea: undefined
2025-05-21 12:17:22 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "string",
    "path": [],
    "message": "Expected object, received string"
  }
] by cosmin.oprea: undefined
2025-05-21 12:17:36 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "string",
    "path": [],
    "message": "Expected object, received string"
  }
] by cosmin.oprea: undefined
2025-05-21 12:18:02 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "string",
    "path": [],
    "message": "Expected object, received string"
  }
] by cosmin.oprea: undefined
2025-05-21 12:19:07 [INFO]: getProductAttributes -> Product "11127502650" has the attributes [object Object],[object Object],[object Object] by cosmin.oprea
2025-05-21 12:19:14 [INFO]: getProductAttributes -> Product "11127502650" has the attributes [object Object],[object Object],[object Object] by cosmin.oprea
2025-05-21 12:39:04 [INFO]: getProductAttributes -> Product "11127502650" has the keys material,marime,colorr and values:  textil,L,red  by cosmin.oprea
2025-05-21 12:39:12 [INFO]: getProductAttributes -> Product "11127502650" has the keys: material,marime,colorr and values:  textil,L,red  by cosmin.oprea
2025-05-21 12:41:11 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material, textil,marime, L,colorr, red by cosmin.oprea
2025-05-21 12:41:25 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red by cosmin.oprea
2025-05-21 12:48:26 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 12:48:26 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 12:54:10 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 12:54:10 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 12:59:12 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 12:59:12 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 13:00:07 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 13:00:07 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 13:01:49 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 13:01:49 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 13:03:05 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 13:03:05 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 13:04:53 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 13:04:54 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 13:06:22 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 13:06:22 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 13:09:41 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 13:09:41 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 13:10:39 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 13:10:46 [INFO]: ProductSearchAction -> Search for 01090398983 with success by cosmin.oprea
2025-05-21 13:10:51 [INFO]: getProductAttributes -> Product "01090398983" has the attributes:  by cosmin.oprea
2025-05-21 13:10:57 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 13:10:59 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 13:22:20 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF by cosmin.oprea
2025-05-21 13:29:00 [INFO]: ProductSearchAction -> Search for 01200426528 with success by cosmin.oprea
2025-05-21 13:29:07 [INFO]: getProductAttributes -> Product "01200426528" has the attributes:  by cosmin.oprea
2025-05-21 13:29:18 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 13:29:19 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF by cosmin.oprea
2025-05-21 13:32:37 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF by cosmin.oprea
2025-05-21 13:32:37 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF by cosmin.oprea
2025-05-21 13:50:26 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF by cosmin.oprea
2025-05-21 13:52:26 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF by cosmin.oprea
2025-05-21 13:52:30 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF by cosmin.oprea
2025-05-21 13:53:49 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF by cosmin.oprea
2025-05-21 13:53:50 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF by cosmin.oprea
2025-05-21 13:54:30 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF by cosmin.oprea
2025-05-21 13:57:51 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF by cosmin.oprea
2025-05-21 13:59:13 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF by cosmin.oprea
2025-05-21 14:00:20 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 14:00:37 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF by cosmin.oprea
2025-05-21 14:02:27 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 14:02:29 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF by cosmin.oprea
2025-05-21 15:34:43 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF by cosmin.oprea
2025-05-21 15:36:14 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF by cosmin.oprea
2025-05-21 15:36:53 [INFO]: ProductSearchAction -> Search for 01090398983 with success by cosmin.oprea
2025-05-21 15:36:54 [INFO]: getProductAttributes -> Product "01090398983" has the attributes:  by cosmin.oprea
2025-05-21 15:37:31 [INFO]: getProductAttributes -> Product "01090398983" has the attributes:  by cosmin.oprea
2025-05-21 15:39:20 [INFO]: getProductAttributes -> Product "01090398983" has the attributes:  by cosmin.oprea
2025-05-21 15:40:49 [INFO]: getProductAttributes -> Product "01090398983" has the attributes:  by cosmin.oprea
2025-05-21 15:41:01 [INFO]: getProductAttributes -> Product "01090398983" has the attributes:  by cosmin.oprea
2025-05-21 15:41:19 [INFO]: getProductAttributes -> Product "01090398983" has the attributes:  by cosmin.oprea
2025-05-21 15:41:50 [INFO]: getProductAttributes -> Product "01090398983" has the attributes:  by cosmin.oprea
2025-05-21 15:41:56 [INFO]: getProductAttributes -> Product "01090398983" has the attributes:  by cosmin.oprea
2025-05-21 15:42:03 [INFO]: getProductAttributes -> Product "01090398983" has the attributes:  by cosmin.oprea
2025-05-21 15:42:24 [INFO]: getProductAttributes -> Product "01090398983" has the attributes:  by cosmin.oprea
2025-05-21 15:42:30 [INFO]: ProductSearchAction -> Search for 01090398983 with success by cosmin.oprea
2025-05-21 15:43:01 [INFO]: getProductAttributes -> Product "01090398983" has the attributes:  by cosmin.oprea
2025-05-21 15:44:26 [INFO]: ProductSearchAction -> Search for 01090398983 with success by cosmin.oprea
2025-05-21 15:44:28 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 15:44:30 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF by cosmin.oprea
2025-05-21 15:44:51 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 15:46:35 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 15:48:02 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 15:49:28 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 15:49:32 [ERROR]: DeleteProductAttributeAction -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "string",
    "received": "undefined",
    "path": [
      "key"
    ],
    "message": "Required"
  }
] by cosmin.oprea: undefined
2025-05-21 15:49:55 [ERROR]: DeleteProductAttributeAction -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "string",
    "received": "undefined",
    "path": [
      "key"
    ],
    "message": "Required"
  }
] by cosmin.oprea: undefined
2025-05-21 15:50:23 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 15:50:35 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 15:51:36 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 15:51:58 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 15:53:56 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 15:55:37 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 15:57:36 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 15:58:29 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 15:58:58 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 15:59:02 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 15:59:52 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 15:59:52 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 15:59:52 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 15:59:53 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 15:59:53 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 16:00:18 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 16:00:19 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 16:00:19 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 16:00:19 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 16:00:19 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: material -- textil,marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 16:00:36 [INFO]: DeleteProductAttributeAction -> deleted with success the key material for the product 11127502650 by cosmin.oprea
2025-05-21 16:00:36 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF,fff -- fdfsfd by cosmin.oprea
2025-05-21 16:00:42 [INFO]: DeleteProductAttributeAction -> deleted with success the key fff for the product 11127502650 by cosmin.oprea
2025-05-21 16:00:42 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: marime -- L,colorr -- red,rere -- reasd,DDDDD -- FFFFF by cosmin.oprea
2025-05-21 16:00:43 [INFO]: DeleteProductAttributeAction -> deleted with success the key DDDDD for the product 11127502650 by cosmin.oprea
2025-05-21 16:00:44 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 16:00:59 [INFO]: ProductSearchAction -> Search for 01090398983 with success by cosmin.oprea
2025-05-21 16:01:05 [INFO]: getProductAttributes -> Product "01090398983" has the attributes:  by cosmin.oprea
2025-05-21 16:01:11 [INFO]: ProductSearchAction -> Search for 01410012466 with success by cosmin.oprea
2025-05-21 16:01:13 [INFO]: getProductAttributes -> Product "01410012466" has the attributes:  by cosmin.oprea
2025-05-21 16:01:35 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 16:03:39 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 16:03:51 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 16:04:08 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: marime -- L,colorr -- red,rere -- reasd by cosmin.oprea
2025-05-21 16:04:12 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: marime -- L,colorr -- red,rere -- reasd,test -- test by cosmin.oprea
2025-05-21 16:04:16 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 16:06:16 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: marime -- L,colorr -- red,rere -- reasd,test -- test by cosmin.oprea
2025-05-21 16:07:21 [INFO]: ProductSearchAction -> Search for 01090398983 with success by cosmin.oprea
2025-05-21 16:07:38 [INFO]: getProductAttributes -> Product "01090398983" has the attributes:  by cosmin.oprea
2025-05-21 16:07:45 [INFO]: getProductAttributes -> Product "01090398983" has the attributes: tee -- tttt by cosmin.oprea
2025-05-21 16:07:53 [INFO]: ProductSearchAction -> Search for 01090398983 with success by cosmin.oprea
2025-05-21 16:15:49 [INFO]: ProductSearchAction -> Search for 11127502650 with success by cosmin.oprea
2025-05-21 16:15:51 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: marime -- L,colorr -- red,rere -- reasd,test -- test by cosmin.oprea
2025-05-21 16:16:21 [INFO]: getProductAttributes -> Product "11127502650" has the attributes: marime -- L,colorr -- red,rere -- reasd,test -- test by cosmin.oprea
2025-05-21 16:48:22 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:23 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:23 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:23 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:24 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:24 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:37 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:37 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:38 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:38 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:38 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:42 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:42 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:42 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:43 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:43 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:48 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:48 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:48 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:48 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:49 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:52 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:53 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:53 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:53 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:53 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:56 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:57 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:57 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:57 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:58 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:59:23 [ERROR]: ProductSearchAction -> Error for 11127502650 with success by cosmin.oprea: undefined
2025-05-22 07:53:36 [ERROR]: ProductSearchAction -> Error for 11127502650 with success by cosmin.oprea: undefined
