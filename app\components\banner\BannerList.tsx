"use client"

import { useState, useTransition } from "react";
import { Banner } from "@/generated/prisma";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Card, 
  CardContent 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { 
  Eye, 
  Pencil, 
  Trash2, 
  MoreHorizontal, 
  ArrowUpDown 
} from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { ReturnAction } from "@/app/actions/actions";


interface BannerListProps {
  banners: Banner[];
  deleteBanner: (id: string) => Promise<ReturnAction>;
}

export default function BannerList({ banners, deleteBanner }: BannerListProps) {
  const [sortField, setSortField] = useState<keyof Banner>("position");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition();

  const sortedBanners = [...banners].sort((a, b) => {
    if ((a[sortField] ?? '') < (b[sortField] ?? '')) return sortDirection === "asc" ? -1 : 1;
    if ((a[sortField] ?? '') > (b[sortField] ?? '')) return sortDirection === "asc" ? 1 : -1;
    return 0;
  });

  const handleSort = (field: keyof Banner) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const handleDelete = async (id: string) => {
    if (confirm("Are you sure you want to delete this banner?")) {
      startTransition(async () => {
        try {
          const result = await deleteBanner(id);
          if (result.status === "SUCCESS") {
            toast.success(result.message);
          } else {
            toast.error(result.message);
          }
        } catch (error) {
          toast.error("Failed to delete banner");
          console.error(error);
        }
      });
    }
  };

  const formatDate = (date: Date | null) => {
    if (!date) return "N/A";
    return new Date(date).toLocaleDateString();
  };

  return (
    <Card>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[80px]">Preview</TableHead>
              <TableHead>
                <div className="flex items-center cursor-pointer" onClick={() => handleSort("title")}>
                  Title
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center cursor-pointer" onClick={() => handleSort("placement")}>
                  Placement
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center cursor-pointer" onClick={() => handleSort("position")}>
                  Position
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center cursor-pointer" onClick={() => handleSort("startDate")}>
                  Start Date
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center cursor-pointer" onClick={() => handleSort("endDate")}>
                  End Date
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </div>
              </TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedBanners.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  No banners found. Create your first banner to get started.
                </TableCell>
              </TableRow>
            ) : (
              sortedBanners.map((banner) => (
                <TableRow key={banner.id}>
                  <TableCell>
                    <img 
                      src={banner.imageUrl} 
                      alt={banner.title} 
                      className="w-16 h-12 object-cover rounded"
                    />
                  </TableCell>
                  <TableCell className="font-medium">{banner.title}</TableCell>
                  <TableCell>{banner.placement}</TableCell>
                  <TableCell>{banner.position}</TableCell>
                  <TableCell>{formatDate(banner.startDate)}</TableCell>
                  <TableCell>{formatDate(banner.endDate)}</TableCell>
                  <TableCell>
                    <Badge variant={banner.isActive ? "default" : "secondary"}>
                      {banner.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <Link href={`/banner/${banner.id}`} passHref>
                          <DropdownMenuItem>
                            <Eye className="mr-2 h-4 w-4" />
                            View
                          </DropdownMenuItem>
                        </Link>
                        <Link href={`/banner/edit/${banner.id}`} passHref>
                          <DropdownMenuItem>
                            <Pencil className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                        </Link>
                        <DropdownMenuItem 
                          onClick={() => handleDelete(banner.id)}
                          disabled={isDeleting === banner.id}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          {isDeleting === banner.id ? "Deleting..." : "Delete"}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
