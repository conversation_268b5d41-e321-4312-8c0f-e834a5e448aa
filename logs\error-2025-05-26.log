{"level":"error","message":"getProductAttributes -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 13:36:48"}
{"level":"error","message":"getProductAttributes -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 13:36:55"}
{"level":"error","message":"getProductAttributes -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 13:37:02"}
{"level":"error","message":"getProductAttributes -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 13:37:29"}
{"level":"error","message":"getProductAttributes -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 13:39:07"}
{"level":"error","message":"getProductAttributes -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 13:39:19"}
{"level":"error","message":"getProductAttributes -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 13:40:07"}
{"level":"error","message":"getProductAttributes -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 13:40:15"}
{"level":"error","message":"getProductAttributes -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 13:40:20"}
{"level":"error","message":"getProductAttributes -> Product \"01200426531\" not found by cosmin.oprea","timestamp":"2025-05-26 13:40:32"}
{"level":"error","message":"getProductAttributes -> Error at parsing: [\n  {\n    \"code\": \"too_big\",\n    \"maximum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 13:43:04"}
{"level":"error","message":"getProductAttributes -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 14:00:38"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      4,\n      \"materialNumber\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-05-26 14:01:05"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Attribute with key \"key1\" already exists for this product by cosmin.oprea","timestamp":"2025-05-26 14:02:13"}
{"level":"error","message":"ProductAddAttributeCSVAction -> Attribute with key \"key1\" already exists for this product by cosmin.oprea","timestamp":"2025-05-26 14:05:53"}
{"level":"error","message":"DiscountUpdateAction -> The discount allready exists: haha  by cosmin.oprea","timestamp":"2025-05-26 16:02:47"}
{"level":"error","message":"DiscountUpdateAction -> The discount allready exists: haha2  by cosmin.oprea","timestamp":"2025-05-26 16:03:36"}
{"level":"error","message":"DiscountUpdateAction -> The discount allready exists: bbbbbbbbbb  by cosmin.oprea","timestamp":"2025-05-26 16:04:15"}
{"level":"error","message":"DiscountUpdateAction -> The discount allready exists: cccccc  by cosmin.oprea","timestamp":"2025-05-26 16:09:03"}
{"level":"error","message":"DiscountUpdateAction -> The discount allready exists: ddddddddddddddddddddddddddd  by cosmin.oprea","timestamp":"2025-05-26 16:16:39"}
