import ProductHistoryPage from "@/app/components/product/ProductHistory";
import prisma from "@/app/utils/db";
import { requireAdmin } from "@/lib/auth-utils";

export default async function ProductHistoryRoute({ params }: { params: Promise<{ material_number: string }> }) {
  await requireAdmin();
  const paramsObject = await params;
    const materialNumber = paramsObject.material_number;    //All params are strings and need to be parsed
    const productHistory = await prisma.productHistory.findMany({
      where: { Material_Number: materialNumber },
      orderBy: {
        version: "asc"
      }
    });

    return (
        <div className="p-4">
          <h1 className="text-2xl font-bold mb-4">History for {materialNumber}</h1>
          <ProductHistoryPage foundProductHistory={productHistory} />
        </div>
    )
}
