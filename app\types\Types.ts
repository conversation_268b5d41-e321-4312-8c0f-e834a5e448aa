import { ChangeType } from "@/generated/prisma";

export interface ServerError {
  fieldErrors?: Record<string, string>;
  message: string;
}


export interface ProductAttribute{
  key: string | null;
  value: string | null;
}

export interface ProductAttributeHistory {
  Material_Number: string;
  version: number;
  change_type: ChangeType;
  changed_by: string;
  changes: JSON;
  snapshot: JSON;
  updatedAt: Date;
}

export interface FoundCategoryDisplay {
  level1: {
    id: string;
    name: string;
    nameRO: string | null;
    imageUrl: string | null; // Add imageUrl from schema
  };
  level2: {
    id: string;
    name: string;
    nameRO: string | null;
    imageUrl: string | null; // Add imageUrl from schema
  };
  level3: {
    id: string;
    name: string;
    nameRO: string | null;
    familyCode: string | null;
    imageUrl: string | null; // Add imageUrl from schema
  };
}

// Define the type for the form state that the server action will return
export interface SearchFormState {
  message: string | null;
  categories: FoundCategoryDisplay[] | null;
  errors: {
    familyCode?: string[];
  } | null;
}

// Initial state for the form
export const initialSearchFormState: SearchFormState = {
  message: null,
  categories: null,
  errors: null,
};

export interface CategoryFetchResult {
  categories: FoundCategoryDisplay[] | null;
  message: string;
  errors?: { familyCode?: string[] };
}


export interface ProductDetailsInterface {
    id: string;
    Material_Number: string;
    Material_Group: string | null;
    Net_Weight: string | null;
    Description_Local: string | null;
    PretAM: string  | null;
    FinalPrice: string | null;
    HasDiscount: boolean;
    activeDiscountType: string | null;
    activeDiscountValue: string | null;
    Base_Unit_Of_Measur: string | null;
    Cross_Plant: string | null;
    New_Material: string | null;
    createdAt: string;
    last_updated_at: string;
    discountPercentage: string | null;
}