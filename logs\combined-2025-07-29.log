{"level":"info","message":"DiscountUpdateAction -> Discount with name pret fix updated <NAME_EMAIL>","timestamp":"2025-07-29 12:37:56"}
{"level":"info","message":"DiscountUpdateAction -> Discount with name cu fix updated <NAME_EMAIL>","timestamp":"2025-07-29 12:38:03"}
{"level":"info","message":"DiscountUpdateAction -> Discount with name pret fix updated <NAME_EMAIL>","timestamp":"2025-07-29 12:38:11"}
{"level":"info","message":"DiscountUpdateAction -> Discount with name <PERSON> tyres updated <NAME_EMAIL>","timestamp":"2025-07-29 12:38:20"}
{"level":"error","message":"DiscountAddProductAction -> Product \"07119963155\" already has a <NAME_EMAIL>","timestamp":"2025-07-29 13:39:02"}
{"level":"info","message":"Starting updateProductPrice for product cmc2a4fv00006hkhx9kbr5lh6","timestamp":"2025-07-29 13:39:38"}
{"level":"info","message":"Found product 01200426532 with base price 4.84","timestamp":"2025-07-29 13:39:38"}
{"level":"info","message":"No active discount found for product 01200426532","timestamp":"2025-07-29 13:39:38"}
{"level":"info","message":"No price change for 01200426532: 4.84","timestamp":"2025-07-29 13:39:38"}
{"level":"info","message":"Updated product 01200426532 with new price 4.84","timestamp":"2025-07-29 13:39:38"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmc2a4fv00006hkhx9kbr5lh6 for the discountId cmc31d58b000hhxm08e6ii38<NAME_EMAIL>","timestamp":"2025-07-29 13:39:38"}
{"level":"info","message":"Starting updateProductPrice for product cmc2a4g13006thkhxd1x1anc0","timestamp":"2025-07-29 13:39:40"}
{"level":"info","message":"Found product 07119963155 with base price 39.28","timestamp":"2025-07-29 13:39:40"}
{"level":"info","message":"No active discount found for product 07119963155","timestamp":"2025-07-29 13:39:40"}
{"level":"info","message":"No price change for 07119963155: 39.28","timestamp":"2025-07-29 13:39:40"}
{"level":"info","message":"Updated product 07119963155 with new price 39.28","timestamp":"2025-07-29 13:39:40"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmc2a4g13006thkhxd1x1anc0 for the discountId cmc31d58b000hhxm08e6ii38<NAME_EMAIL>","timestamp":"2025-07-29 13:39:40"}
{"level":"info","message":"Starting updateProductPrice for product cmc2a4g5p00ckhkhx0e5h2bee","timestamp":"2025-07-29 13:39:43"}
{"level":"info","message":"Found product 11122246093 with base price 559.33","timestamp":"2025-07-29 13:39:43"}
{"level":"info","message":"No active discount found for product 11122246093","timestamp":"2025-07-29 13:39:43"}
{"level":"info","message":"No price change for 11122246093: 559.33","timestamp":"2025-07-29 13:39:43"}
{"level":"info","message":"Updated product 11122246093 with new price 559.33","timestamp":"2025-07-29 13:39:43"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmc2a4g5p00ckhkhx0e5h2bee for the discountId cmc31d58b000hhxm08e6ii38<NAME_EMAIL>","timestamp":"2025-07-29 13:39:43"}
{"level":"info","message":"Starting updateProductPrice for product cmc2a4p3t05lthkhxb3lagi70","timestamp":"2025-07-29 13:39:49"}
{"level":"info","message":"Found product 12317501755 with base price 245.23","timestamp":"2025-07-29 13:39:49"}
{"level":"info","message":"No active discount found for product 12317501755","timestamp":"2025-07-29 13:39:49"}
{"level":"info","message":"No price change for 12317501755: 245.23","timestamp":"2025-07-29 13:39:49"}
{"level":"info","message":"Updated product 12317501755 with new price 245.23","timestamp":"2025-07-29 13:39:49"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmc2a4p3t05lthkhxb3lagi70 for the discountId cmc31c4xk0004hxm0ivirsti1 by <EMAIL>","timestamp":"2025-07-29 13:39:49"}
{"level":"info","message":"Starting updateProductPrice for product cmc2a4fzg004rhkhxc1wohir0","timestamp":"2025-07-29 13:39:54"}
{"level":"info","message":"Found product 11000435439 with base price 39830.84","timestamp":"2025-07-29 13:39:54"}
{"level":"info","message":"No active discount found for product 11000435439","timestamp":"2025-07-29 13:39:54"}
{"level":"info","message":"No price change for 11000435439: 39830.84","timestamp":"2025-07-29 13:39:54"}
{"level":"info","message":"Updated product 11000435439 with new price 39830.84","timestamp":"2025-07-29 13:39:54"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmc2a4fzg004rhkhxc1wohir0 for the discountId cmbuqdkl00003hxv4vm6utec5 by <EMAIL>","timestamp":"2025-07-29 13:39:54"}
{"level":"info","message":"Starting updateProductPrice for product cmc2a4oze05k7hkhxe6oxfcql","timestamp":"2025-07-29 13:39:55"}
{"level":"info","message":"Found product 11531743192 with base price 193.77","timestamp":"2025-07-29 13:39:55"}
{"level":"info","message":"No active discount found for product 11531743192","timestamp":"2025-07-29 13:39:55"}
{"level":"info","message":"No price change for 11531743192: 193.77","timestamp":"2025-07-29 13:39:55"}
{"level":"info","message":"Updated product 11531743192 with new price 193.77","timestamp":"2025-07-29 13:39:55"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmc2a4oze05k7hkhxe6oxfcql for the discountId cmbuqdkl00003hxv4vm6utec5 by <EMAIL>","timestamp":"2025-07-29 13:39:55"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmc2a4g13006thkhxd1x1anc0 (07119963155)","timestamp":"2025-07-29 13:41:07"}
{"level":"info","message":"Starting updateProductPrice for product cmc2a4g13006thkhxd1x1anc0","timestamp":"2025-07-29 13:41:07"}
{"level":"info","message":"Found product 07119963155 with base price 39.28","timestamp":"2025-07-29 13:41:07"}
{"level":"info","message":"Found active discount for 07119963155: NEW_PRICE with value 11","timestamp":"2025-07-29 13:41:07"}
{"level":"info","message":"NEW_PRICE: New price set to 11","timestamp":"2025-07-29 13:41:07"}
{"level":"info","message":"Discount applied: 07119963155 - Original: 39.28, Final: 11, Discount %: 71.995926680244399185","timestamp":"2025-07-29 13:41:07"}
{"level":"info","message":"Price changed for 07119963155: 39.28 -> 11","timestamp":"2025-07-29 13:41:07"}
{"level":"info","message":"Updated product 07119963155 with new price 11","timestamp":"2025-07-29 13:41:07"}
{"level":"info","message":"Created price history record for 07119963155","timestamp":"2025-07-29 13:41:07"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-07-29 13:41:07"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmc31d58b000hhxm08e6ii38s updated <NAME_EMAIL>","timestamp":"2025-07-29 13:41:07"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmc2a54ub0at2hkhxgbm95iqn (31336753477)","timestamp":"2025-07-29 13:42:50"}
{"level":"info","message":"Starting updateProductPrice for product cmc2a54ub0at2hkhxgbm95iqn","timestamp":"2025-07-29 13:42:50"}
{"level":"info","message":"Found product 31336753477 with base price 723.99","timestamp":"2025-07-29 13:42:50"}
{"level":"info","message":"Found active discount for 31336753477: FIXED_AMOUNT with value 5","timestamp":"2025-07-29 13:42:50"}
{"level":"info","message":"FIXED_AMOUNT: 723.99 - 5 = 718.99","timestamp":"2025-07-29 13:42:50"}
{"level":"info","message":"Discount applied: 31336753477 - Original: 723.99, Final: 718.99, Discount %: 0.69061727371925026589","timestamp":"2025-07-29 13:42:50"}
{"level":"info","message":"Price changed for 31336753477: 723.99 -> 718.99","timestamp":"2025-07-29 13:42:50"}
{"level":"info","message":"Updated product 31336753477 with new price 718.99","timestamp":"2025-07-29 13:42:50"}
{"level":"info","message":"Created price history record for 31336753477","timestamp":"2025-07-29 13:42:50"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-07-29 13:42:50"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmc31c4xk0004hxm0ivirsti1 updated <NAME_EMAIL>","timestamp":"2025-07-29 13:42:50"}
