"use server";

import { revalidatePath } from "next/cache";
import prisma from "@/app/utils/db";
import { logError, logInfo } from "@/lib/logger";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { ReturnAction } from "./actions";

interface CategoryCSVData {
  level1Name: string;
  level1NameRO?: string;
  level1ImageUrl?: string;
  level2Name: string;
  level2NameRO?: string;
  level2ImageUrl?: string;
  level3Name: string;
  level3NameRO?: string;
  level3ImageUrl?: string;
  familyCode: string;
}

export async function uploadCategoriesCSVAction(data: CategoryCSVData[]): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email;

  if (!data || data.length === 0) {
    logError(`uploadCategoriesCSVAction -> No data provided by ${userEmail}`);
    return {
      status: "ERROR",
      message: "No data provided in the CSV file.",
    };
  }

  try {
    let processedCount = 0;

    // Use a transaction to ensure all operations succeed or fail together
    await prisma.$transaction(async (tx) => {
      for (const item of data) {
        // Find or create level 1
        let level1 = await tx.categoryLevel1.findFirst({
          where: { name: item.level1Name }
        });

        if (!level1) {
          level1 = await tx.categoryLevel1.create({
            data: {
              name: item.level1Name,
              nameRO: item.level1NameRO || null,
              imageUrl: item.level1ImageUrl || null,
            }
          });
        }

        // Find or create level 2
        let level2 = await tx.categoryLevel2.findFirst({
          where: { 
            name: item.level2Name,
            level1Id: level1.id
          }
        });

        if (!level2) {
          level2 = await tx.categoryLevel2.create({
            data: {
              name: item.level2Name,
              nameRO: item.level2NameRO || null,
              imageUrl: item.level2ImageUrl || null,
              level1Id: level1.id
            }
          });
        }

        // Find or create level 3
        let level3 = await tx.categoryLevel3.findFirst({
          where: { 
            name: item.level3Name,
            level2Id: level2.id
          }
        });

        if (!level3) {
          level3 = await tx.categoryLevel3.create({
            data: {
              name: item.level3Name,
              nameRO: item.level3NameRO || null,
              imageUrl: item.level3ImageUrl || null,
              familyCode: item.familyCode,
              level2Id: level2.id
            }
          });
          processedCount++;
        } else if (level3.familyCode !== item.familyCode) {
          // Update family code if it changed
          await tx.categoryLevel3.update({
            where: { id: level3.id },
            data: { familyCode: item.familyCode }
          });
          processedCount++;
        }
      }
    });

    revalidatePath('/categories');
    logInfo(`uploadCategoriesCSVAction -> Successfully processed ${processedCount} categories by ${userEmail}`);
    
    return {
      status: "SUCCESS",
      message: `Successfully processed ${processedCount} categories!`,
    };
  } catch (error) {
    logError(`uploadCategoriesCSVAction -> Error: ${error} by ${userEmail}`);
    return {
      status: "ERROR",
      message: "An error occurred while processing the CSV file.",
    };
  }
}