import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import DiscountCreateForm from "@/app/components/discount/DiscountCreateForm";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { DiscountCreateAction } from "@/app/actions/actions";

export const metadata: Metadata = {
  title: "Create Discount",
  description: "Create a new discount",
};

export default async function CreateDiscountPage() {
  await requireAdminOrModerator();
  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center gap-4 mb-6">
        <Link href="/discounts">
          <Button variant="outline">Back to Discounts</Button>
        </Link>
        <h1 className="text-3xl font-bold">Create New Discount</h1>
      </div>
      
      <DiscountCreateForm createAction={DiscountCreateAction} />
    </div>
  );
}