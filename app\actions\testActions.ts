"use server";

import prisma from "../utils/db";
import { revalidatePath } from "next/cache";
import { logError, logInfo } from "@/lib/logger";
import { Decimal } from "@prisma/client/runtime/library";

interface ReturnAction {
  status: "SUCCESS" | "ERROR";
  message: string;
  data?: any;
}

/**
 * Adds random test products to an existing order
 * For testing purposes only - not for production use
 */
export async function addTestProductsToOrder(orderId: string, count: number = 3): Promise<ReturnAction> {
  try {
    // Verify the order exists
    const order = await prisma.order.findUnique({
      where: { id: orderId },
    });

    if (!order) {
      return {
        status: "ERROR",
        message: "Order not found",
      };
    }

    // Get random products from the database
    const randomProducts = await prisma.product.findMany({
      where: {
        FinalPrice: { not: null }, // Only get products with prices
      },
      take: count,
      orderBy: {
        // Get a random selection of products
        id: "asc",
      },
    });

    if (randomProducts.length === 0) {
      return {
        status: "ERROR",
        message: "No products available to add",
      };
    }

    // Create order items for each product
    const orderItems = randomProducts.map((product) => ({
      orderId,
      productId: product.id,
      quantity: Math.floor(Math.random() * 3) + 1, // Random quantity between 1-3
      price: product.FinalPrice || new Decimal(0),
      notes: "Test product added automatically",
    }));

    // Add the order items to the database
    await prisma.orderItem.createMany({
      data: orderItems,
    });

    // Calculate the new order total
    const allOrderItems = await prisma.orderItem.findMany({
      where: { orderId },
      select: {
        quantity: true,
        price: true,
      },
    });

    const newTotal = allOrderItems.reduce(
      (sum, item) => sum.plus(item.price.mul(item.quantity)),
      new Decimal(0)
    );

    // Update the order with the new total
    await prisma.order.update({
      where: { id: orderId },
      data: {
        amount: newTotal,
        updatedBy: "userEmail",
      },
    });

    // Create a status history entry
    await prisma.orderStatusHistory.create({
      data: {
        orderId,
        orderStatus: order.orderStatus,
        notes: `Added ${orderItems.length} test products to order`,
        changedBy: "userEmail",
      },
    });

    // Revalidate the order page
    revalidatePath(`/orders/${orderId}`);

    logInfo(`Added ${orderItems.length} test products to order ${orderId} by ${"userEmail"}`);
    
    return {
      status: "SUCCESS",
      message: `Added ${orderItems.length} test products to order`,
    };
  } catch (error) {
    logError(`Error adding test products to order: ${error}`);
    return {
      status: "ERROR",
      message: "Failed to add test products to order",
    };
  }
}

/**
 * Creates a test order for development/testing purposes
 * Not for production use
 */
export async function createTestOrder(): Promise<ReturnAction> {
  try {
    // Find a test user with addresses
    let testUser = await prisma.user.findFirst({
      include: {
        billingAddresses: true,
        shippingAddresses: true
      }
    });
    
    // If no user with addresses exists, create one
    if (!testUser || !testUser.billingAddresses.length || !testUser.shippingAddresses.length) {
      // First check if we have any user at all
      if (!testUser) {
        testUser = await prisma.user.create({
          data: {
            email: "<EMAIL>",
            firstName: "Test",
            lastName: "User",
            role: "inregistratAB", // Adjust based on your enum values
            externalId: `test-${Date.now()}`, // Generate a unique external ID
            isActive: true,
            createdBy: "system",
            updatedBy: "system",
            profileImage: "https://ui-avatars.com/api/?name=Test+User"
          },
          include: {
            billingAddresses: true,
            shippingAddresses: true
          }
        });
      }
      
      // Create a billing address if needed
      if (!testUser.billingAddresses.length) {
        await prisma.billingAddress.create({
          data: {
            userId: testUser.id,
            fullName: "Test User",
            address: "123 Test Street",
            city: "Test City",
            county: "Test County",
          }
        });
      }
      
      // Create a shipping address if needed
      if (!testUser.shippingAddresses.length) {
        await prisma.shippingAddress.create({
          data: {
            userId: testUser.id,
            fullName: "Test User",
            address: "123 Test Street",
            city: "Test City",
            county: "Test County",
            notes: "Test shipping address",
            phoneNumber: "1234567890"
          }
        });
      }
      
      // Fetch the user again with the newly created addresses
      testUser = await prisma.user.findUnique({
        where: { id: testUser.id },
        include: {
          billingAddresses: true,
          shippingAddresses: true
        }
      });
    }

    // Create a random order number
    const orderNumber = `TEST-${Date.now().toString().slice(-6)}`;
    
    // Create a new order with minimal data
    const order = await prisma.order.create({
      data: {
        orderNumber,
        amount: new Decimal(0), // Start with zero amount
        orderStatus: "plasata",
        paymentStatus: "asteptare",
        paymentMethod: "ramburs",
        shippingMethod: "curier",
        shipmentStatus: "asteptare",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        
        // Connect to existing user and addresses
        userId: testUser?.id || "<EMAIL>",
        billingAddressId: testUser?.billingAddresses[0].id || "<EMAIL>",
        shippingAddressId: testUser?.shippingAddresses[0].id || "<EMAIL>",
      },
    });

    // Create a status history entry
    await prisma.orderStatusHistory.create({
      data: {
        orderId: order.id,
        orderStatus: "plasata",
        notes: "Test order created",
        changedBy: "<EMAIL>",
      },
    });

    logInfo(`Created test order ${order.id} with order number ${orderNumber}`);
    
    // Revalidate the orders page
    revalidatePath('/orders');
    
    return {
      status: "SUCCESS",
      message: `Created test order #${orderNumber}`,
      data: { orderId: order.id, orderNumber }
    };
  } catch (error) {
    logError(`Error creating test order: ${error}`);
    return {
      status: "ERROR",
      message: "Failed to create test order",
    };
  }
}




