import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import OrderList from "../components/order/OrderList";
import { getOrders } from "../getData/order/getOrders";
import { Order as PrismaOrder } from "@/generated/prisma";
import { Order } from "@/app/types/order";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import CreateTestOrder from "../components/order/CreateTestOrder";

export const metadata: Metadata = {
  title: "Order Management",
  description: "View and manage customer orders",
};

export default async function OrdersRoute() {
  await requireAdminOrModerator();
  const orders = await getOrders();

  return (
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Order Management</h1>
          <div className="flex gap-2">
            <Link href="/orders/create">
              <Button>Create New Order</Button>
            </Link>
            <CreateTestOrder />
          </div>
        </div>
        
        <OrderList orders={orders as unknown as Order[]} />
      </div>
  );
}
