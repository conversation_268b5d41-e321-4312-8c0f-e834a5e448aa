"use server";
//NU LE FOLOSESC
import prisma from "../utils/db";
import { logError, logInfo } from "@/lib/logger";
import { revalidatePath } from "next/cache";
import { updateProductPrice } from "../utils/utils";
import { requireAdminOrModerator } from "@/lib/auth-utils";

/**
 * Updates prices for all products associated with a specific discount.
 * This is useful when a discount's terms change and all affected products need updating.
 * 
 * @param discountId - The ID of the discount whose products need price updates
 * @returns Object with status and message
 */
export async function updatePricesForDiscount(discountId: string) {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  try {
    // Find all products with this discount
    const productDiscounts = await prisma.productDiscount.findMany({
      where: { discountId },
      select: { productId: true },
    });
    
    // Update each product's price
    const productIds = productDiscounts.map(pd => pd.productId);
    const results = await Promise.all(
      productIds.map(id => updateProductPrice(id))
    );
    
    // Count successful updates
    const successCount = results.filter(Boolean).length;
    
    logInfo(`Updated prices for ${successCount} of ${productIds.length} products for discount ${discountId} by ${userEmail}`);
    
    // Revalidate relevant paths
    revalidatePath(`/discounts/${discountId}`);
    revalidatePath('/products');
    
    return {
      status: "SUCCESS",
      message: `Updated prices for ${successCount} products.`,
    };
  } catch (error) {
    logError(`updatePricesForDiscount error: ${error} by ${userEmail}`);
    return {
      status: "ERROR",
      message: "Failed to update product prices.",
    };
  }
}

/**
 * Updates prices for all products in the database.
 * This is useful for bulk operations or when discount rules change globally.
 * Processes products in batches to avoid overwhelming the server.
 * 
 * @returns Object with status and message
 */
export async function updateAllProductPrices() {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  try {
    // Get all product IDs
    const products = await prisma.product.findMany({
      select: { id: true },
    });
    
    // Process in batches to avoid overwhelming the server
    const batchSize = 50;
    let successCount = 0;
    
    for (let i = 0; i < products.length; i += batchSize) {
      const batch = products.slice(i, i + batchSize);
      const results = await Promise.all(
        batch.map(p => updateProductPrice(p.id))
      );
      
      successCount += results.filter(Boolean).length;
      
      // Log progress for long operations
      logInfo(`Updated ${successCount} of ${products.length} products`);
    }
    
    // Revalidate relevant paths
    revalidatePath('/products');
    
    return {
      status: "SUCCESS",
      message: `Updated prices for ${successCount} of ${products.length} products.`,
    };
  } catch (error) {
    logError(`updateAllProductPrices error: ${error}`);
    return {
      status: "ERROR",
      message: "Failed to update product prices.",
    };
  }
}

/**
 * Updates the price for a single product.
 * This is used when a specific product's details change or when it's added/removed from a discount.
 * 
 * @param productId - The ID of the product to update
 * @returns Object with status and message
 */
export async function updateSingleProductPrice(productId: string) {
  try {
    const success = await updateProductPrice(productId);
    
    if (success) {
      // Get the product's material number for path revalidation
      const product = await prisma.product.findUnique({
        where: { id: productId },
        select: { Material_Number: true },
      });
      
      if (product) {
        revalidatePath(`/products/${product.Material_Number}`);
        revalidatePath('/products');
      }
      
      return {
        status: "SUCCESS",
        message: "Product price updated successfully.",
      };
    } else {
      return {
        status: "ERROR",
        message: "Failed to update product price.",
      };
    }
  } catch (error) {
    logError(`updateSingleProductPrice error: ${error}`);
    return {
      status: "ERROR",
      message: "An error occurred while updating the product price.",
    };
  }
}
