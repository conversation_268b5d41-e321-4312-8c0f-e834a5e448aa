"use client"

import Form from "next/form";
import { SubmitButton } from "../buttons";
import { Input } from "@/components/ui/input";

interface ProductSearchFormProps {
  initialMaterialNumber?: string; // Prop to pre-fill the input from searchParams
  serverErrors?: string[]; // Prop to receive server-side validation errors
}

export function ProductSearchForm({ initialMaterialNumber, serverErrors }: ProductSearchFormProps) {

  return (
    <div className="mb-4">
      <Form action="" className="flex gap-4">
          <Input
            id="materialNumber"
            name="materialNumber" // Crucial: This 'name' becomes the search param key
            defaultValue={initialMaterialNumber || ''} // Use defaultValue for uncontrolled input
            className='w-full'
            placeholder="Material Number Search Term:"
          />
        <SubmitButton /> {/* The button automatically interacts with the parent Form */}
      </Form>
        {serverErrors && serverErrors.length > 0 && (
            <p className="mt-1 text-sm text-red-600">{serverErrors.join(', ')}</p>
        )}
    </div>
  );
}


