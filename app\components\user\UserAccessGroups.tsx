"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import { Rol, UserGroup } from "@/generated/prisma";
import { UserCog, Plus, X, Shield, AlertTriangle } from "lucide-react";
import { updateUser, addUserToGroup, removeUserFromGroup } from "@/app/actions/userActions";

interface UserAccessGroupsProps {
  userId: string;
  userRole: string;
  accessGroups: UserGroup[];
  availableGroups: UserGroup[];
}

export default function UserAccessGroups({ 
  userId, 
  userRole, 
  accessGroups = [], 
  availableGroups = [] 
}: UserAccessGroupsProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<string>("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const roleLabels: Record<string, string> = {
    "administAB": "Administrator",
    "moderatorAB": "Moderator",
    "angajatAB": "Employee",
    "inregistratAB": "User",
    "fourLvlAdminAB": "L4 Admin",
    "fourLvlInregistratAB": "L4 User",
  };

  async function addGroupToUser() {
    if (!selectedGroup) return;
    
    setIsSubmitting(true);
    
    try {
      const result = await addUserToGroup({
        userId,
        groupId: selectedGroup
      });
      
      if (result.status === "SUCCESS") {
        toast.success("Access group added successfully");
        setIsDialogOpen(false);
        setSelectedGroup("");
        router.refresh();
      } else {
        toast.error(result.message || "Failed to add access group");
      }
    } catch (error) {
      console.error("Error adding group:", error);
      toast.error("Failed to add access group");
    } finally {
      setIsSubmitting(false);
    }
  }

  async function removeGroupFromUser(groupId: string) {
    setIsSubmitting(true);
    
    try {
      const result = await removeUserFromGroup({
        userId,
        groupId
      });
      
      if (result.status === "SUCCESS") {
        toast.success("Access group removed successfully");
        router.refresh();
      } else {
        toast.error(result.message || "Failed to remove access group");
      }
    } catch (error) {
      console.error("Error removing group:", error);
      toast.error("Failed to remove access group");
    } finally {
      setIsSubmitting(false);
    }
  }

  async function changeUserRole(newRole: Rol) {
    setIsSubmitting(true);
    
    try {
      const result = await updateUser({
        id: userId,
        role: newRole
      });
      
      if (result.status === "SUCCESS") {
        toast.success("User role updated successfully");
        router.refresh();
      } else {
        toast.error(result.message || "Failed to update user role");
      }
    } catch (error) {
      console.error("Error changing role:", error);
      toast.error("Failed to update user role");
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">User Role</h3>
          <Badge variant="outline" className="px-3">
            <Shield className="mr-1 h-3.5 w-3.5" />
            {roleLabels[userRole] || "User"}
          </Badge>
        </div>
        
        <div className="grid grid-cols-1 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Change user role</p>
                  <p className="text-sm text-muted-foreground">
                    This will change the user's base permissions in the system
                  </p>
                </div>
                <Select
                  value={userRole}
                  onValueChange={(value) => {
                    if (confirm("Are you sure you want to change this user's role?")) {
                      changeUserRole(value as Rol);
                    }
                  }}
                  disabled={isSubmitting}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="inregistratAB">User</SelectItem>
                    <SelectItem value="angajatAB">Employee</SelectItem>
                    <SelectItem value="moderatorAB">Moderator</SelectItem>
                    <SelectItem value="administAB">Administrator</SelectItem>
                    <SelectItem value="fourLvlInregistratAB">L4 User</SelectItem>
                    <SelectItem value="fourLvlAdminAB">L4 Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Access Groups</h3>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm" disabled={availableGroups.length === 0}>
                <Plus className="mr-1 h-4 w-4" />
                Add Group
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add Access Group</DialogTitle>
                <DialogDescription>
                  Assign this user to an access group to grant additional permissions.
                </DialogDescription>
              </DialogHeader>
              
              <div className="py-4">
                <Select value={selectedGroup} onValueChange={setSelectedGroup}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a group" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableGroups.map((group) => (
                      <SelectItem key={group.id} value={group.id}>
                        {group.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={addGroupToUser} disabled={!selectedGroup || isSubmitting}>
                  {isSubmitting ? "Adding..." : "Add Group"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
        
        {accessGroups.length === 0 ? (
          <div className="rounded-lg border border-dashed p-8 text-center">
            <UserCog className="mx-auto h-10 w-10 text-muted-foreground/60" />
            <h3 className="mt-4 text-lg font-semibold">No Access Groups</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              This user is not a member of any access groups.
            </p>
            <Button 
              className="mt-4" 
              variant="outline" 
              onClick={() => setIsDialogOpen(true)}
              disabled={availableGroups.length === 0}
            >
              <Plus className="mr-2 h-4 w-4" />
              Add to Group
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-4">
            {accessGroups.map((group) => (
              <Card key={group.id}>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium">{group.name}</p>
                      {group.description && (
                        <p className="text-sm text-muted-foreground">
                          {group.description}
                        </p>
                      )}
                      {group.permissions && group.permissions.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {group.permissions.slice(0, 3).map((permission) => (
                            <Badge key={permission} variant="secondary" className="text-xs">
                              {permission}
                            </Badge>
                          ))}
                          {group.permissions.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{group.permissions.length - 3} more
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => {
                        if (confirm(`Remove user from ${group.name} group?`)) {
                          removeGroupFromUser(group.id);
                        }
                      }}
                      disabled={isSubmitting}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
      
      <div className="rounded-lg bg-amber-50 p-4 text-amber-900 dark:bg-amber-950 dark:text-amber-200">
        <div className="flex items-start">
          <AlertTriangle className="mr-3 h-5 w-5 flex-shrink-0" />
          <div>
            <h4 className="font-medium">Important Note</h4>
            <p className="text-sm mt-1">
              Changes to user roles and access groups take effect immediately. 
              Make sure you understand the permissions you are granting.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

