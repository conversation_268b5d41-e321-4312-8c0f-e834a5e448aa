{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmc2a4p3t05lthkhxb3lagi70 (12317501755)","timestamp":"2025-06-18 21:42:46"}
{"level":"info","message":"Starting updateProductPrice for product cmc2a4p3t05lthkhxb3lagi70","timestamp":"2025-06-18 21:42:46"}
{"level":"info","message":"Found product 12317501755 with base price 100","timestamp":"2025-06-18 21:42:46"}
{"level":"info","message":"Found active discount for 12317501755: PERCENTAGE with value 25","timestamp":"2025-06-18 21:42:46"}
{"level":"info","message":"PERCENTAGE: 100 - 25% = 75","timestamp":"2025-06-18 21:42:46"}
{"level":"info","message":"Discount applied: 12317501755 - Original: 100, Final: 75, Discount %: 25","timestamp":"2025-06-18 21:42:46"}
{"level":"info","message":"Price changed for 12317501755: 100 -> 75","timestamp":"2025-06-18 21:42:46"}
{"level":"info","message":"Updated product 12317501755 with new price 75","timestamp":"2025-06-18 21:42:46"}
{"level":"info","message":"Created price history record for 12317501755","timestamp":"2025-06-18 21:42:46"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-18 21:42:46"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbuqdkl00003hxv4vm6utec5 updated <NAME_EMAIL>","timestamp":"2025-06-18 21:42:46"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmc2a4oze05k7hkhxe6oxfcql (11531743192)","timestamp":"2025-06-18 21:42:50"}
{"level":"info","message":"Starting updateProductPrice for product cmc2a4oze05k7hkhxe6oxfcql","timestamp":"2025-06-18 21:42:50"}
{"level":"info","message":"Found product 11531743192 with base price 1000","timestamp":"2025-06-18 21:42:50"}
{"level":"info","message":"Found active discount for 11531743192: PERCENTAGE with value 25","timestamp":"2025-06-18 21:42:50"}
{"level":"info","message":"PERCENTAGE: 1000 - 25% = 750","timestamp":"2025-06-18 21:42:50"}
{"level":"info","message":"Discount applied: 11531743192 - Original: 1000, Final: 750, Discount %: 25","timestamp":"2025-06-18 21:42:50"}
{"level":"info","message":"Price changed for 11531743192: 1000 -> 750","timestamp":"2025-06-18 21:42:50"}
{"level":"info","message":"Updated product 11531743192 with new price 750","timestamp":"2025-06-18 21:42:50"}
{"level":"info","message":"Created price history record for 11531743192","timestamp":"2025-06-18 21:42:50"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-18 21:42:50"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbuqdkl00003hxv4vm6utec5 updated <NAME_EMAIL>","timestamp":"2025-06-18 21:42:50"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmc2a4fzg004rhkhxc1wohir0 (11000435439)","timestamp":"2025-06-18 21:42:54"}
{"level":"info","message":"Starting updateProductPrice for product cmc2a4fzg004rhkhxc1wohir0","timestamp":"2025-06-18 21:42:54"}
{"level":"info","message":"Found product 11000435439 with base price 500","timestamp":"2025-06-18 21:42:54"}
{"level":"info","message":"Found active discount for 11000435439: PERCENTAGE with value 25","timestamp":"2025-06-18 21:42:54"}
{"level":"info","message":"PERCENTAGE: 500 - 25% = 375","timestamp":"2025-06-18 21:42:54"}
{"level":"info","message":"Discount applied: 11000435439 - Original: 500, Final: 375, Discount %: 25","timestamp":"2025-06-18 21:42:54"}
{"level":"info","message":"Price changed for 11000435439: 500 -> 375","timestamp":"2025-06-18 21:42:54"}
{"level":"info","message":"Updated product 11000435439 with new price 375","timestamp":"2025-06-18 21:42:54"}
{"level":"info","message":"Created price history record for 11000435439","timestamp":"2025-06-18 21:42:54"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-18 21:42:54"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbuqdkl00003hxv4vm6utec5 updated <NAME_EMAIL>","timestamp":"2025-06-18 21:42:54"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmc2a4fv00006hkhx9kbr5lh6 (01200426532)","timestamp":"2025-06-18 21:43:05"}
{"level":"info","message":"Starting updateProductPrice for product cmc2a4fv00006hkhx9kbr5lh6","timestamp":"2025-06-18 21:43:05"}
{"level":"info","message":"Found product 01200426532 with base price 200","timestamp":"2025-06-18 21:43:05"}
{"level":"info","message":"Found active discount for 01200426532: PERCENTAGE with value 25","timestamp":"2025-06-18 21:43:05"}
{"level":"info","message":"PERCENTAGE: 200 - 25% = 150","timestamp":"2025-06-18 21:43:05"}
{"level":"info","message":"Discount applied: 01200426532 - Original: 200, Final: 150, Discount %: 25","timestamp":"2025-06-18 21:43:05"}
{"level":"info","message":"Price changed for 01200426532: 200 -> 150","timestamp":"2025-06-18 21:43:05"}
{"level":"info","message":"Updated product 01200426532 with new price 150","timestamp":"2025-06-18 21:43:05"}
{"level":"info","message":"Created price history record for 01200426532","timestamp":"2025-06-18 21:43:05"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-18 21:43:05"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbuqdkl00003hxv4vm6utec5 updated <NAME_EMAIL>","timestamp":"2025-06-18 21:43:05"}
