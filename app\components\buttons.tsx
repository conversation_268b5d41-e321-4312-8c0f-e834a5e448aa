"use client"

import { useFormStatus } from "react-dom";
import { Button } from "@/components/ui/button";
import { ReactNode } from "react";

interface SubmitButtonProps {
  children?: ReactNode;
}

export function SubmitButton({ children }: SubmitButtonProps) {
  const { pending } = useFormStatus();

  return (
    <Button type="submit" disabled={pending}>
      {pending ? "Loading..." : children || "Submit"}
    </Button>
  );
}
