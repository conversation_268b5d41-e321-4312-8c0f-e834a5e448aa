"use client";

import { useState, useTransition } from "react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import <PERSON> from 'papaparse';
import { DiscountProcessCSVAction } from "@/app/actions/actions";

interface AddProductsFromCSVProps {
  discountId: string;
}

export default function AddProductsFromCSV({ discountId }: AddProductsFromCSVProps) {
  const [isPending, startTransition] = useTransition();
  const [file, setFile] = useState<File | null>(null);
  const [serverInvalidProducts, setServerInvalidProducts] = useState<string[] | null>(null)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    setFile(selectedFile || null);
  };

  const handleFileUpload = () => {
    if (!file) {
      toast.error("Please select a CSV file.");
      return;
    }

    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: async (results) => {
        const data = results.data as { material_number: string }[];

        const materialNumbers = data
          .map((row) => row.material_number)
          .filter(Boolean)
          .map((number) => {
          // Ensure length is 11 characters by padding with a leading "0"
          return number.length === 10 ? `0${number}` : number;
        });
console.log("materialNumbers", materialNumbers);
          setServerInvalidProducts(null)
        startTransition(async () => {

          try {
            const response = await DiscountProcessCSVAction({ discountId, materialNumbers });
            
            if(response.status === "SUCCESS"){
              toast.success(response.message);
            }else{
              setServerInvalidProducts(response.invalidProducts ?? null);
              toast.error(response.message)
            }

          } catch (error) {
                const parsedError = typeof error === "string" ? JSON.parse(error) : { message: "Unknown error" };

                if (parsedError.existingProducts?.length) {
                    toast.error(
                    `Some products are already in a discount: ${parsedError.existingProducts.join(
                        ", "
                    )}`
                    );
                } else {
                    toast.error(parsedError.message || "An error occurred.");
                }
            }
        });
      }
    });
  };

  return (
    <>
      <div className="flex">
        {/* <h3 className="text-lg font-bold">Upload CSV</h3> */}
        <input type="file" accept=".csv" onChange={handleFileChange} />
        <Button
          variant="default"
          onClick={handleFileUpload}
          disabled={isPending || !file}
        >
          {isPending ? "Processing..." : "Upload and Add Products"}
        </Button>
      </div>
      {serverInvalidProducts && <p className="text-red-500">{} {serverInvalidProducts.join(" - ")}</p> }
    </>
  );
}
