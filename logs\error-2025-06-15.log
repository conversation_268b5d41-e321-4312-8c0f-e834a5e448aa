{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:05:01"}
{"level":"error","message":"updateProductPrice: Product cmbuoiyb205lt9whxhklceafb not found or has no base price","timestamp":"2025-06-15 00:05:59"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:06:00"}
{"level":"error","message":"updateProductPrice: Product cmbuoiyb205lt9whxhklceafb not found or has no base price","timestamp":"2025-06-15 00:06:03"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:06:03"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:32:35"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:32:41"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:32:50"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:36:15"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:36:18"}
{"level":"error","message":"DiscountProcessCSVAction -> No material numbers provided  by unauthenticatedUser","timestamp":"2025-06-15 00:38:02"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:38:03"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:39:59"}
{"level":"error","message":"DiscountProcessCSVAction -> No material numbers provided  by unauthenticatedUser","timestamp":"2025-06-15 00:40:04"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:40:05"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:41:04"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:41:05"}
{"level":"error","message":"DiscountProcessCSVAction -> No material numbers provided  by unauthenticatedUser","timestamp":"2025-06-15 00:41:21"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:41:22"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:43:55"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-15 00:43:56"}
{"level":"error","message":"DiscountProcessCSVAction -> No material numbers provided  by <EMAIL>","timestamp":"2025-06-15 00:44:51"}
{"level":"error","message":"DiscountProcessCSVAction -> No material numbers provided  by <EMAIL>","timestamp":"2025-06-15 00:46:19"}
{"level":"error","message":"DiscountProcessCSVAction -> No material numbers provided  by <EMAIL>","timestamp":"2025-06-15 00:47:54"}
{"level":"error","message":"DiscountProcessCSVAction -> No material numbers provided  by <EMAIL>","timestamp":"2025-06-15 00:49:01"}
{"level":"error","message":"DiscountProcessCSVAction -> No material numbers provided  by <EMAIL>","timestamp":"2025-06-15 00:50:18"}
{"level":"error","message":"updateProductPrice: Product cmbuoiua200009whxgr3pg7p4 not found or has no base price","timestamp":"2025-06-15 01:09:31"}
{"level":"error","message":"DiscountAddProductAction -> Product \"11531743192\" already has a <NAME_EMAIL>","timestamp":"2025-06-15 02:22:08"}
{"level":"error","message":"DiscountAddProductAction -> Product \"11531743192\" already has a <NAME_EMAIL>","timestamp":"2025-06-15 02:47:39"}
