"server-only"

import prisma from "@/app/utils/db";
import { Rol, Prisma } from "@/generated/prisma";
import { logError, logInfo } from "@/lib/logger";
import { auth, currentUser } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { cache } from "react";

interface GetUsersParams {
  query?: string;
  role?: string;
  status?: string;
  page?: number;
  perPage?: number;
  sort?: string;
  order?: "asc" | "desc";
}

//export async function getCurrentUser() {
export const getCurrentUser = cache(async () => {
  try {
    const { userId } = await auth();
    if (!userId) {
      return null;
    }
    
    const user = await prisma.user.findFirst({
      where: { externalId: userId },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        profileImage: true,
        role: true,
        isActive: true,
        isSuspended: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true,
        deletedAt: true,
        userAM: true,
        phoneNumber: true,
        department: true,
        jobTitle: true,
        accessGroups: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    if (!user) {
      return null;
    }
    
    return user;
  } catch (error) {
    logError("Error fetching current user:", { error });
    return null;
  }
})

export const getUsers = cache(async ({
  query,
  role,
  status,
  page = 1,
  perPage = 4,
  sort = "createdAt",
  order = "desc"
}: GetUsersParams) => {
  try {
    const where: Prisma.UserWhereInput = {};
    
    // Apply filters
    if (query) {
      where.OR = [
        { email: { contains: query, mode: 'insensitive' } },
        { firstName: { contains: query, mode: 'insensitive' } },
        { lastName: { contains: query, mode: 'insensitive' } },
        { userAM: { contains: query, mode: 'insensitive' } },
        { phoneNumber: { contains: query, mode: 'insensitive' } },
      ];
    }
    
    if (role) {
      where.role = role as Rol;
    }
    
    if (status) {
      switch (status) {
        case 'active':
          where.isActive = true;
          where.isSuspended = false;
          where.deletedAt = null;
          break;
        case 'inactive':
          where.isActive = false;
          where.deletedAt = null;
          break;
        case 'suspended':
          where.isSuspended = true;
          where.deletedAt = null;
          break;
        case 'deleted':
          where.deletedAt = { not: null };
          break;
      }
    } else {
      // By default, exclude deleted users
      where.deletedAt = null;
    }
    
    // Calculate pagination
    const skip = (page - 1) * perPage;
    
    // Validate sort field (security measure)
    const validSortFields = [
      'createdAt', 'updatedAt', 'email', 'firstName', 
      'lastName', 'lastLoginAt', 'loginCount', 'role'
    ];
    const safeSort = validSortFields.includes(sort) ? sort : 'createdAt';
    
    // Execute query with pagination
    const [users, totalUsers] = await Promise.all([
      prisma.user.findMany({
        where,
        orderBy: { [safeSort]: order },
        skip,
        take: perPage,
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          profileImage: true,
          role: true,
          isActive: true,
          isSuspended: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true,
          deletedAt: true,
          userAM: true,
          phoneNumber: true,
          department: true,
          jobTitle: true,
          accessGroups: {
            select: {
              id: true,
              name: true
            }
          }
        }
      }),
      prisma.user.count({ where })
    ]);
    
    const totalPages = Math.ceil(totalUsers / perPage);
    
    return { users, totalUsers, totalPages };
  } catch (error) {
    logError("Error fetching users:", { error });
    throw new Error("Failed to fetch users");
  }
})

export async function getUserById(id: string) {

  try {
    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        accessGroups: true,
        auditLogs: {
          take: 10,
          orderBy: { createdAt: 'desc' }
        }
      },
    });
    
    if (!user) {
      logError(`User with ID ${id} not found`, { userId: id });
      return null;
    }
    
    logInfo(`User ${id} details retrieved for ${"userEmail"}` );
    return user;
  } catch (error) {
    logError(`Error fetching user with ID ${id}:`, { error, userId: id });
    return null;
  }
}

export async function getUsersByRole(role: Rol) {
  try {
    const users = await prisma.user.findMany({
      where: { 
        role,
        deletedAt: null 
      },
      orderBy: { 
        lastName: 'asc' 
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        role: true,
        profileImage: true
      }
    });
    
    return users;
  } catch (error) {
    logError(`Error fetching users with role ${role}:`, { error, role });
    throw new Error("Failed to fetch users by role");
  }
}

export async function getUsersWithAccessGroup(groupId: string) {
  try {
    const users = await prisma.user.findMany({
      where: {
        accessGroups: {
          some: {
            id: groupId
          }
        },
        deletedAt: null
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        role: true,
        profileImage: true
      },
      orderBy: {
        lastName: 'asc'
      }
    });
    
    return users;
  } catch (error) {
    logError(`Error fetching users with access group ${groupId}:`, { error, groupId });
    throw new Error("Failed to fetch users by access group");
  }
}

export async function getRecentlyActiveUsers(limit = 5) {
  try {
    const users = await prisma.user.findMany({
      where: {
        lastLoginAt: { not: null },
        deletedAt: null
      },
      orderBy: {
        lastLoginAt: 'desc'
      },
      take: limit,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        profileImage: true,
        lastLoginAt: true
      }
    });
    
    return users;
  } catch (error) {
    logError("Error fetching recently active users:", { error });
    throw new Error("Failed to fetch recently active users");
  }
}

export async function getNewUsers(days = 30, limit = 5) {
  try {
    const date = new Date();
    date.setDate(date.getDate() - days);
    
    const users = await prisma.user.findMany({
      where: {
        createdAt: { gte: date },
        deletedAt: null
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: limit,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        profileImage: true,
        createdAt: true
      }
    });
    
    return users;
  } catch (error) {
    logError(`Error fetching new users from last ${days} days:`, { error, days });
    throw new Error("Failed to fetch new users");
  }
}

export async function getUserAuditLogs(userId: string, limit = 20) {
  try {
    const logs = await prisma.userAuditLog.findMany({
      where: {
        userId
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: limit
    });
    
    return logs;
  } catch (error) {
    logError(`Error fetching audit logs for user ${userId}:`, { error, userId });
    throw new Error("Failed to fetch user audit logs");
  }
}


export async function getUserAccessGroups(userId: string) {
  try {
    const accessGroups = await prisma.userGroup.findMany({
      where: {
        users: {
          some: {
            id: userId
          }
        }
      },
      select: {
        id: true,
        name: true,
        description: true,
        permissions: true
      }
    });
    
    return accessGroups;
  } catch (error) {
    logError(`Error fetching access groups for user ${userId}:`, { error, userId });
    throw new Error("Failed to fetch user access groups");
  }
}
