{"level":"info","message":"Discount with name last test created successfully by cosmin.oprea","timestamp":"2025-06-13 10:27:04"}
{"level":"info","message":"Starting expired discount check at 2025-06-13T07:27:24.487Z","timestamp":"2025-06-13 10:27:24"}
{"level":"info","message":"Found 1 expired discounts to process","timestamp":"2025-06-13 10:27:24"}
{"level":"info","message":"Processed expired discount cmbhxsh5h0006hxngai17nk9l: deactivated and removed 0 products","timestamp":"2025-06-13 10:27:24"}
{"level":"error","message":"DiscountUpdateAction -> Error at parsing: [\n  {\n    \"code\": \"custom\",\n    \"message\": \"End date cannot be in the past\",\n    \"path\": [\n      \"endDate\"\n    ]\n  }\n] by cosmin.oprea","timestamp":"2025-06-13 10:28:00"}
{"level":"info","message":"Discount with name cu procentaj updated successfully by cosmin.oprea","timestamp":"2025-06-13 10:28:04"}
{"level":"info","message":"Starting expired discount check at 2025-06-13T07:28:37.034Z","timestamp":"2025-06-13 10:28:37"}
{"level":"info","message":"Found 0 expired discounts to process","timestamp":"2025-06-13 10:28:37"}
{"level":"info","message":"Starting expired discount check at 2025-06-13T07:29:05.305Z","timestamp":"2025-06-13 10:29:05"}
{"level":"info","message":"Found 1 expired discounts to process","timestamp":"2025-06-13 10:29:05"}
{"level":"info","message":"Processed expired discount cmbhxr0en0000hxngzzwgceol: deactivated and removed 0 products","timestamp":"2025-06-13 10:29:05"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbhwx48405lvg4hxabjf4igx (12317501755)","timestamp":"2025-06-13 10:29:23"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx48405lvg4hxabjf4igx","timestamp":"2025-06-13 10:29:23"}
{"level":"info","message":"Found product 12317501755 with base price 55","timestamp":"2025-06-13 10:29:23"}
{"level":"info","message":"Found active discount for 12317501755: PERCENTAGE with value 10","timestamp":"2025-06-13 10:29:23"}
{"level":"info","message":"PERCENTAGE: 55 - 10% = 49.5","timestamp":"2025-06-13 10:29:23"}
{"level":"info","message":"Discount applied: 12317501755 - Original: 55, Final: 49.5, Discount %: 10","timestamp":"2025-06-13 10:29:23"}
{"level":"info","message":"Price changed for 12317501755: 55 -> 49.5","timestamp":"2025-06-13 10:29:23"}
{"level":"info","message":"Updated product 12317501755 with new price 49.5","timestamp":"2025-06-13 10:29:23"}
{"level":"info","message":"Created price history record for 12317501755","timestamp":"2025-06-13 10:29:23"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-13 10:29:23"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbuhinkv0000hxc8j402wd6f updated successfully by cosmin.oprea","timestamp":"2025-06-13 10:29:23"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx48405lvg4hxabjf4igx","timestamp":"2025-06-13 10:37:34"}
{"level":"info","message":"Found product 12317501755 with base price 55","timestamp":"2025-06-13 10:37:34"}
{"level":"info","message":"No active discount found for product 12317501755","timestamp":"2025-06-13 10:37:34"}
{"level":"info","message":"Price changed for 12317501755: 49.5 -> 55","timestamp":"2025-06-13 10:37:34"}
{"level":"info","message":"Updated product 12317501755 with new price 55","timestamp":"2025-06-13 10:37:34"}
{"level":"info","message":"Created price history record for 12317501755","timestamp":"2025-06-13 10:37:34"}
{"level":"info","message":"DiscountDeleteProductAction -> deleted with success the productId cmbhwx48405lvg4hxabjf4igx for the discountId cmbuhinkv0000hxc8j402wd6f by cosmin.oprea","timestamp":"2025-06-13 10:37:34"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmbhwx48405lvg4hxabjf4igx (12317501755)","timestamp":"2025-06-13 10:40:10"}
{"level":"info","message":"Starting updateProductPrice for product cmbhwx48405lvg4hxabjf4igx","timestamp":"2025-06-13 10:40:10"}
{"level":"info","message":"Found product 12317501755 with base price 95","timestamp":"2025-06-13 10:40:10"}
{"level":"info","message":"Found active discount for 12317501755: PERCENTAGE with value 10","timestamp":"2025-06-13 10:40:10"}
{"level":"info","message":"PERCENTAGE: 95 - 10% = 85.5","timestamp":"2025-06-13 10:40:10"}
{"level":"info","message":"Discount applied: 12317501755 - Original: 95, Final: 85.5, Discount %: 10","timestamp":"2025-06-13 10:40:10"}
{"level":"info","message":"Price changed for 12317501755: 55 -> 85.5","timestamp":"2025-06-13 10:40:10"}
{"level":"info","message":"Updated product 12317501755 with new price 85.5","timestamp":"2025-06-13 10:40:10"}
{"level":"info","message":"Created price history record for 12317501755","timestamp":"2025-06-13 10:40:10"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Success","timestamp":"2025-06-13 10:40:10"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmbuhinkv0000hxc8j402wd6f updated successfully by cosmin.oprea","timestamp":"2025-06-13 10:40:10"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-13 14:28:04"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-13 14:32:16"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-13 14:32:34"}
{"level":"info","message":"Discount with name Summer tyres created successfully by unauthenticatedUser","timestamp":"2025-06-13 14:35:04"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-13 14:35:09"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-13 14:50:13"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-13 14:50:14"}
{"level":"error","message":"Error fetching current user:: [object Object]","timestamp":"2025-06-13 14:50:15"}
