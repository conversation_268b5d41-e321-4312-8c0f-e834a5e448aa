"use server";

import { revalidatePath } from "next/cache";
import prisma from "@/app/utils/db";
import { logError, logInfo } from "@/lib/logger";
import { 
  UpdateUserFormValues, 
  ChangeUserPasswordFormValues,
  ChangeUserStatusFormValues,
  updateUserSchema,
  changeUserPasswordSchema,
  changeUserStatusSchema,
  AddUserToGroupFormValues,
  RemoveUserFromGroupFormValues,
  removeUserFromGroupSchema,
  addUserToGroupSchema
} from "@/app/zod/userSchemas";
import { Rol } from "@/generated/prisma";
import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { ReturnAction } from "./actions";
import { clerkClient } from "@clerk/nextjs/server";


export async function updateUser(data: UpdateUserFormValues): Promise<ReturnAction>{
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email

  if (!actor || (actor.role !== 'administAB' && actor.role !== 'moderatorAB') || !actor.isActive || actor.isSuspended) {
    redirect("/unauthorized");
  }

  try {
    // Validate input data
    const validatedData = updateUserSchema.parse(data);
    const { id, ...updateData } = validatedData;

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id }
    });
    
    if (!existingUser) {
      logError(`User not found: ${id} by ${actorEmail}`)
      return {
        status: "ERROR",
        message: "User not found",
      };
    }
    
    // Track changes for audit log
    const changes: Record<string, { from: any; to: any }> = {};

    // Update user in database
    const user = await prisma.$transaction(async (tx) => {
      
      // Compare old and new values
      Object.keys(updateData).forEach(key => {
        const typedKey = key as keyof typeof updateData;
        if (updateData[typedKey] !== existingUser[typedKey as keyof typeof existingUser]) {
          changes[key] = {
            from: existingUser[typedKey as keyof typeof existingUser],
            to: updateData[typedKey]
          };
        }
      });
      
      // Update the user
      const updatedUser = await tx.user.update({
        where: { id },
        data: {
          ...updateData,
          updatedBy: actorEmail,
          updatedAt: new Date()
        }
      });
      
      // Create audit log entry if there were changes
      if (Object.keys(changes).length > 0) {
        await tx.userAuditLog.create({
          data: {
            entityType: "user",
            entityId: updatedUser.id,
            userId: updatedUser.id,
            action: "USER_UPDATED",
            details: `User ${actorEmail} updated: ${JSON.stringify(changes)}`,
            ipAddress: "server-side",
            userAgent: "server-side",
            performedBy: actorEmail
          }
        });
      }
      
      return updatedUser;
    });
    
    // Revalidate relevant paths
    revalidatePath("/users");
    revalidatePath(`/users/${id}`);
    
    logInfo(`User updated successfully: ${user.email} by ${actorEmail} with changes: ${JSON.stringify(changes)}`);
    return {
      status: "SUCCESS",
      message: "User updated successfully",
    };
  } catch (error) {
    logError(`Error updating user: ${error} by ${actorEmail}`);
    return {
      status: "ERROR",
      message: "Failed to update user",
    };
  }
}

export async function changeUserPassword(data: ChangeUserPasswordFormValues) {
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email
  try {
    // Validate input data
    const validatedData = changeUserPasswordSchema.parse(data);
    const { userId, newPassword } = validatedData;
    
    // In a real app, you would hash the password before storing it
    // For Clerk integration, use their API to reset the password
    
    // Update audit log
    await prisma.userAuditLog.create({
      data: {
        userId,
        entityType: "user",
        entityId: userId,
        action: "PASSWORD_CHANGED",
        details: "Password was changed",
        ipAddress: "server-side",
        userAgent: "server-side",
        performedBy: actorEmail
      }
    });
    
    // Revalidate relevant paths
    revalidatePath(`/users/${userId}`);
    
    logInfo(`Password changed for user ${userId} by ${actorEmail}`);
    
    return { success: true };
  } catch (error) {
    logError(`Error changing user password: ${error} by ${actorEmail}`);
    return { success: false };
  }
}

export async function changeUserStatus(data: ChangeUserStatusFormValues): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email
  try {
    // Validate input data
    const validatedData = changeUserStatusSchema.parse(data);
    const { userId, isActive, isSuspended, suspensionReason } = validatedData;
    
    // Get current user status
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { isActive: true, isSuspended: true, suspensionReason: true }
    });
    
    if (!user) {
        logError(`User not found: ${userId} by ${actorEmail}`)
      return {
        status: "ERROR",
        message: "User not found",
      };
    }
    
    // Update user status
    const updatedUser = await prisma.$transaction(async (tx) => {
      // Update the user
      const updated = await tx.user.update({
        where: { id: userId },
        data: {
          isActive,
          isSuspended,
          suspensionReason: isSuspended ? suspensionReason : null,
          updatedBy: actorEmail,
          updatedAt: new Date()
        }
      });
      
      // Determine the action type
      let action = "USER_STATUS_CHANGED";
      if (isActive && !user.isActive) action = "USER_ACTIVATED";
      if (!isActive && user.isActive) action = "USER_DEACTIVATED";
      if (isSuspended && !user.isSuspended) action = "USER_SUSPENDED";
      if (!isSuspended && user.isSuspended) action = "USER_UNSUSPENDED";
      
      // Create audit log entry
      await tx.userAuditLog.create({
        data: {
          userId,
          entityType: "user",
          entityId: userId,
          action,
          details: isSuspended && suspensionReason 
            ? `Status changed. Suspension reason: ${suspensionReason}`
            : "User status changed",
          ipAddress: "server-side",
          userAgent: "server-side",
          performedBy: actorEmail
        }
      });
      
      return updated;
    });
    
    // Revalidate relevant paths
    revalidatePath("/users");
    revalidatePath(`/users/${userId}`);
    
    logInfo(`User status changed for user ${userId} with changes: ${JSON.stringify({ isActive, isSuspended, suspensionReason })} by ${actorEmail}`);
    
    return {
      status: "SUCCESS",
      message: "User status changed successfully",
    };
  } catch (error) {
    logError(`Error changing user status: ${error} by ${actorEmail}`);
    return {
      status: "ERROR",
      message: "Failed to change user status",
    };
  }
}

export async function deleteUser(userId: string) {
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email

  if(!userId) return { success: false };

  try {
    
    // Soft delete the user
    const user = await prisma.$transaction(async (tx) => {
      // Update the user
      const deletedUser = await tx.user.update({
        where: { id: userId },
        data: {
          deletedAt: new Date(),
          updatedBy: actorEmail,
          updatedAt: new Date()
        }
      });
      
      // Create audit log entry
      await tx.userAuditLog.create({
        data: {
          userId,
          entityType: "user",
          entityId: userId,
          action: "USER_DELETED",
          details: "User was soft-deleted",
          ipAddress: "server-side",
          userAgent: "server-side",
          performedBy: actorEmail
        }
      });
      
      return deletedUser;
    });
    
    // Revalidate relevant paths
    revalidatePath("/users");
    
    logInfo(`User deleted: ${user.email} by ${actorEmail}`);
    
    return { success: true };
  } catch (error) {
    logError(`Error deleting user: ${error} by ${actorEmail}`);
    return { success: false };
  }
}

export async function restoreUser(userId: string) {
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email

  try {
    
    // Restore the soft-deleted user
    const user = await prisma.$transaction(async (tx) => {
      // Update the user
      const restoredUser = await tx.user.update({
        where: { id: userId },
        data: {
          deletedAt: null,
          updatedBy: actorEmail,
          updatedAt: new Date()
        }
      });
      
      // Create audit log entry
      await tx.userAuditLog.create({
        data: {
          entityType: "user",
          entityId: userId,
          userId,
          action: "USER_RESTORED",
          details: "User was restored from deleted state",
          ipAddress: "server-side",
          userAgent: "server-side",
          performedBy: actorEmail
        }
      });
      
      return restoredUser;
    });
    
    // Revalidate relevant paths
    revalidatePath("/users");
    revalidatePath(`/users/${userId}`);
    
    logInfo(`User restored: ${user.email} by ${actorEmail}`);
    
    return { success: true };
  } catch (error) {
    logError(`Error restoring user: ${error} by ${actorEmail}`);
    return { success: false };
  }
}

export async function addUserToGroup(data: AddUserToGroupFormValues): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email
  try {
    // Validate input data
    const validatedData = addUserToGroupSchema.parse(data);
    const { userId, groupId } = validatedData;
    
    // Check if user is already in the group
    const existingMembership = await prisma.userGroup.findFirst({
      where: {
        id: groupId,
        users: {
          some: {
            id: userId
          }
        }
      }
    });
    
    if (existingMembership) {
      logError(`User is already a member of this group: ${userId} in ${groupId} by ${actorEmail}`)
      return {
        status: "ERROR",
        message: "User is already a member of this group",
      };
    }
    
    // Add user to group
    await prisma.$transaction(async (tx) => {
      // Update the user-group relationship
      await tx.userGroup.update({
        where: { id: groupId },
        data: {
          users: {
            connect: { id: userId }
          }
        }
      });
      
      // Get group name for the audit log
      const group = await tx.userGroup.findUnique({
        where: { id: groupId },
        select: { name: true }
      });
      
      // Create audit log entry
      await tx.userAuditLog.create({
        data: {
          userId,
          entityType: "user",
          entityId: userId,
          action: "USER_ADDED_TO_GROUP",
          details: `User added to group: ${group?.name || groupId}`,
          ipAddress: "server-side",
          userAgent: "server-side",
          performedBy: actorEmail
        }
      });
    });
    
    // Revalidate relevant paths
    revalidatePath(`/users/${userId}`);
    revalidatePath(`/groups/${groupId}`);
    
    logInfo(`User ${userId} added to group ${groupId} by ${actorEmail}`);
    
    return {
      status: "SUCCESS",
      message: "User added to group successfully",
    };
  } catch (error) {
    logError(`Error adding user to group: ${error} by ${actorEmail}`);
    return {
      status: "ERROR",
      message: "Failed to add user to group",
    };
  }
}

export async function removeUserFromGroup(data: RemoveUserFromGroupFormValues): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email
  try {
    // Validate input data
    const validatedData = removeUserFromGroupSchema.parse(data);
    const { userId, groupId } = validatedData;
    
    // Remove user from group
    await prisma.$transaction(async (tx) => {
      // Get group name for the audit log
      const group = await tx.userGroup.findUnique({
        where: { id: groupId },
        select: { name: true }
      });
      
      // Update the user-group relationship
      await tx.userGroup.update({
        where: { id: groupId },
        data: {
          users: {
            disconnect: { id: userId }
          }
        }
      });
      
      // Create audit log entry
      await tx.userAuditLog.create({
        data: {
          entityType: "user",
          entityId: userId,
          userId,
          action: "USER_REMOVED_FROM_GROUP",
          details: `User removed from group: ${group?.name || groupId}`,
          ipAddress: "server-side",
          userAgent: "server-side",
          performedBy: actorEmail
        }
      });
    });
    
    // Revalidate relevant paths
    revalidatePath(`/users/${userId}`);
    revalidatePath(`/groups/${groupId}`);
    
    logInfo(`User ${userId} removed from group ${groupId} by ${actorEmail}`);
    
    return {
      status: "SUCCESS",
      message: "User removed from group successfully",
    };
  } catch (error) {
    logError(`Error removing user from group: ${error} by ${actorEmail}`);
    return {
      status: "ERROR",
      message: "Failed to remove user from group",
    };
  }
}

export async function recordUserLogin(userId: string, ipAddress?: string, userAgent?: string) {
  try {
    // Update user login information
    await prisma.user.update({
      where: { id: userId },
      data: {
        lastLoginAt: new Date(),
        loginCount: { increment: 1 },
        lastActivityAt: new Date()
      }
    });
    
    // Create audit log entry
    await prisma.userAuditLog.create({
      data: {
        entityType: "user",
        entityId: userId,
        userId,
        action: "USER_LOGIN",
        details: "User logged in",
        ipAddress: ipAddress || "unknown",
        userAgent: userAgent || "unknown",
        performedBy: userId // Self-performed action
      }
    });
    
    logInfo(`User login recorded: ${userId} from ${ipAddress}`);
    
    return { success: true };
  } catch (error) {
    logError("Error recording user login:", { error, userId });
    // Don't throw here - login should succeed even if recording fails
    return { success: false };
  }
}

export async function syncUsers(): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email;

  try {
    // Get all users from Clerk
    const clerk = await clerkClient();
    const clerkUsers = await clerk.users.getUserList({
      limit: 100, // Adjust as needed
    });
    
    // Process each Clerk user
    let syncCount = 0;
    let newCount = 0;
    let updatedCount = 0;
    
    for (const clerkUser of clerkUsers.data) {
      // Skip users without email
      const primaryEmail = clerkUser.emailAddresses.find(
        email => email.id === clerkUser.primaryEmailAddressId
      );
      
      if (!primaryEmail?.emailAddress) continue;
      
      // Check if user already exists in our database
      const existingUser = await prisma.user.findFirst({
        where: { externalId: clerkUser.id }
      });
      
      // Prepare user data from Clerk
      const userData = {
        email: primaryEmail.emailAddress,
        firstName: clerkUser.firstName || "Unknown",
        lastName: clerkUser.lastName || "Unknown",
        profileImage: clerkUser.imageUrl || "",
        externalId: clerkUser.id,
        externalProvider: "clerk",
        emailVerified: primaryEmail.verification?.status === "verified" 
          ? new Date() 
          : null,
        isActive: !clerkUser.banned,
        lastLoginAt: clerkUser.lastSignInAt ? new Date(clerkUser.lastSignInAt) : null,
        updatedBy: actorEmail,
        updatedAt: new Date()
      };
      
      if (existingUser) {
        // Update existing user
        await prisma.user.update({
          where: { id: existingUser.id },
          data: userData
        });
        updatedCount++;
      } else {
        // Create new user with default role
        await prisma.user.create({
          data: {
            ...userData,
            role: "inregistratAB", // Default role for new users
            profileImage: clerkUser.imageUrl || "https://ui-avatars.com/api/?name=" + 
              encodeURIComponent(`${clerkUser.firstName || ""} ${clerkUser.lastName || ""}`),
          }
        });
        newCount++;
      }
      
      syncCount++;
    }
    
    // Log the sync operation
    logInfo(`User sync completed by ${actorEmail}: ${syncCount} users processed, ${newCount} new, ${updatedCount} updated`);
    
    // Revalidate relevant paths
    revalidatePath("/users");
    
    return {
      status: "SUCCESS",
      message: `Users synchronized successfully: ${syncCount} users processed, ${newCount} new, ${updatedCount} updated`,
    };
  } catch (error) {
    logError(`Error syncing users: ${error} by ${actorEmail}`);
    return {
      status: "ERROR",
      message: "Failed to synchronize users",
    };
  }
}


// export async function createUser(data: CreateUserFormValues) {
//   try {
//     // Validate input data
//     const validatedData = createUserSchema.parse(data);
//     const actorEmail = await getCurrentUserEmail();
    
//     // Check if user with this email already exists
//     const existingUser = await prisma.user.findUnique({
//       where: { email: validatedData.email }
//     });
    
//     if (existingUser) {
//       throw new Error("A user with this email already exists");
//     }
    
//     // Create user in database
//     const user = await prisma.$transaction(async (tx) => {
//       // Create the user
//       const newUser = await tx.user.create({
//         data: {
//           ...validatedData,
//           createdBy: actorEmail,
//           updatedBy: actorEmail,
//         }
//       });
      
//       // Create audit log entry
//       await tx.userauditlog.create({
//         data: {
//           userId: newUser.id,
//           action: "USER_CREATED",
//           details: `User created with email ${newUser.email}`,
//           ipAddress: "server-side", // In a real app, you'd capture the client IP
//           userAgent: "server-side", // In a real app, you'd capture the client user agent
//           performedBy: actorEmail
//         }
//       });
      
//       return newUser;
//     });
    
//     // Revalidate relevant paths
//     revalidatePath("/users");
    
//     logInfo(`User created successfully: ${user.email}`, { 
//       userId: user.id, 
//       actorEmail 
//     });
    
//     return user;
//   } catch (error) {
//     logError("Error creating user:", { error, data });
//     throw error;
//   }
// }



