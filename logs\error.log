2025-05-16 15:03:27 [ERROR]: createDiscountAction -> A discount with this name already exists: xxx
2025-05-16 15:04:43 [ERROR]: updateDiscountAction -> The discount allready exists: : xxxhgdhdhd
2025-05-16 15:05:15 [ERROR]: updateDiscountAction -> The discount allready exists: : xxx
2025-05-16 15:05:31 [ERROR]: updateDiscountAction -> The discount allready exists: : xxx
2025-05-16 15:22:34 [ERROR]: updateDiscountAction -> The discount allready exists: : xxx
2025-05-16 15:22:53 [ERROR]: updateDiscountAction -> The discount allready exists: : xxx
2025-05-16 15:22:56 [ERROR]: updateDiscountAction -> The discount allready exists: : xxxf
2025-05-16 15:33:20 [ERROR]: updateDiscountAction -> The discount allready exists: : xxx
2025-05-16 16:08:02 [ERROR]: updateDiscountAction -> The discount allready exists: : xxx
2025-05-16 16:09:09 [ERROR]: discountAddProductAction -> Product 01090398983 already has an active discount.: undefined
2025-05-16 16:11:54 [ERROR]: createDiscountAction -> A discount with this name already exists: xxx
2025-05-16 16:26:06 [ERROR]: discountAddProductAction -> Product 11127502650 already has an active discount.: undefined
2025-05-16 16:26:28 [ERROR]: discountAddProductAction -> Product 01090398983 already has an active discount.: undefined
2025-05-16 16:28:05 [ERROR]: discountAddProductAction -> Product 01410012466 already has an active discount.: undefined
2025-05-16 16:28:12 [ERROR]: discountAddProductAction -> Product 01090398983 already has an active discount.: undefined
2025-05-16 17:07:01 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      0
    ]
  }
]
2025-05-16 17:07:09 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      0
    ]
  }
]
2025-05-16 17:07:33 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      0
    ]
  }
]
2025-05-16 17:08:10 [ERROR]: processCSVDiscountProductAction -> No products found in DB for the provided material numbers: undefined
2025-05-16 17:08:48 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01410012466
2025-05-16 17:09:17 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01410012466
2025-05-16 17:09:25 [ERROR]: discountAddProductAction -> Product 01410012466 already has an active discount.: undefined
2025-05-16 17:09:29 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01410012466
2025-05-16 17:09:55 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01090398983,01410012466
2025-05-17 08:37:10 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01090398983,01410012466
2025-05-17 08:48:09 [ERROR]: processCSVDiscountProductAction -> No products found in DB for the provided material numbers: undefined
2025-05-17 09:20:01 [ERROR]: processCSVDiscountProductAction -> No material numbers provided: undefined
2025-05-17 09:22:24 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      0
    ]
  }
]
2025-05-17 09:22:52 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      0
    ]
  }
]
2025-05-17 09:23:35 [ERROR]: processCSVDiscountProductAction -> Missing products: 1.23123E+11: undefined
2025-05-17 09:23:53 [ERROR]: processCSVDiscountProductAction -> Missing products: 1.23123E+11: undefined
2025-05-17 09:24:20 [ERROR]: processCSVDiscountProductAction -> Missing products: 1.23123E+11: undefined
2025-05-17 09:26:32 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01200426532
2025-05-17 09:28:06 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01200426532
2025-05-17 09:28:15 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01200426532
2025-05-17 09:35:29 [ERROR]: processCSVDiscountProductAction -> Missing products: 9999999991h: undefined
2025-05-17 09:35:57 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      2
    ]
  }
]
2025-05-17 09:36:57 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      2
    ]
  }
]
2025-05-17 09:37:27 [ERROR]: processCSVDiscountProductAction -> Missing products: 9999999991h, 123123tgbhy: undefined
2025-05-17 09:39:29 [ERROR]: processCSVDiscountProductAction -> Missing products: 9999999991h, 123123tgbhy: undefined
2025-05-17 09:39:34 [ERROR]: processCSVDiscountProductAction -> Missing products: 9999999991h, 123123tgbhy: undefined
2025-05-17 09:39:52 [ERROR]: processCSVDiscountProductAction -> Missing products: 9999999991h, 123123tgbhy: undefined
2025-05-17 09:39:56 [ERROR]: processCSVDiscountProductAction -> Missing products: 9999999991h, 123123tgbhy: undefined
2025-05-17 22:54:16 [ERROR]: processCSVDiscountProductAction -> Missing products: 9999999991h, 123123tgbhy: undefined
2025-05-17 22:55:17 [ERROR]: processCSVDiscountProductAction -> Missing products: 9999999991h, 123123tgbhy: undefined
2025-05-17 22:55:48 [ERROR]: processCSVDiscountProductAction -> Missing products: 123123tgbhy: undefined
2025-05-17 23:08:06 [ERROR]: processCSVDiscountProductAction -> Missing products: 123123tgbhy: undefined
2025-05-17 23:08:41 [ERROR]: processCSVDiscountProductAction -> Missing products: 123ERTGB56Y: undefined
2025-05-17 23:08:48 [ERROR]: processCSVDiscountProductAction -> Missing products: 123ERTGB56Y: undefined
2025-05-17 23:08:54 [ERROR]: processCSVDiscountProductAction -> Missing products: 123ERTGB56Y: undefined
2025-05-17 23:09:02 [ERROR]: processCSVDiscountProductAction -> Missing products: 123ERTGB56Y: undefined
2025-05-17 23:09:10 [ERROR]: processCSVDiscountProductAction -> Missing products: 123ERTGB56Y: undefined
2025-05-17 23:12:03 [ERROR]: processCSVDiscountProductAction -> Missing products: 123ERTGB56Y: undefined
2025-05-17 23:14:15 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01090398983
2025-05-17 23:14:26 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01090398983
2025-05-17 23:14:58 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01090398983
2025-05-17 23:29:10 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01090398983
2025-05-17 23:30:07 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      2
    ]
  }
]
2025-05-17 23:31:24 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      2
    ]
  }
]
2025-05-17 23:55:24 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      2
    ]
  }
]
2025-05-17 23:56:36 [ERROR]: discountAddProductAction -> Product 01090398983 already has an active discount.: undefined
2025-05-17 23:57:15 [ERROR]: discountAddProductAction -> Product "0109039898x" not found.: undefined
2025-05-17 23:57:41 [ERROR]: discountAddProductAction -> Product "ff09039898x" not found.: undefined
2025-05-18 00:00:41 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      2
    ]
  }
]
2025-05-18 00:00:45 [ERROR]: processCSVDiscountProductAction -> Error at parsing: : [
  {
    "code": "too_small",
    "minimum": 11,
    "type": "string",
    "inclusive": true,
    "exact": true,
    "message": "Material Number must be exactly 11 characters",
    "path": [
      "materialNumbers",
      2
    ]
  }
]
2025-05-18 00:02:16 [ERROR]: processCSVDiscountProductAction -> Some products are already associated with a discount: : 01090398983,01200426532,64115A1BDB6
2025-05-18 00:07:33 [ERROR]: discountAddProductAction -> Product 01200426528 already has an active discount.: undefined
2025-05-18 00:38:00 [ERROR]: discountAddProductAction -> Product 11127502650 already has an active discount.: undefined
2025-05-18 00:39:42 [ERROR]: discountAddProductAction -> Product 01200426528 already has an active discount.: undefined
2025-05-19 09:04:28 [ERROR]: discountAddProductAction -> Product 01090398983 already has an active discount.: undefined
2025-05-19 09:04:33 [ERROR]: discountAddProductAction -> Product 01200426528 already has an active discount.: undefined
2025-05-19 10:17:16 [ERROR]: discountAddProductAction -> Database error: : 
Invalid `prisma.productDiscount.create()` invocation:


Unique constraint failed on the fields: (`productId`)
2025-05-19 10:17:21 [ERROR]: discountAddProductAction -> Database error: : 
Invalid `prisma.productDiscount.create()` invocation:


Unique constraint failed on the fields: (`productId`)
2025-05-19 10:17:32 [ERROR]: discountAddProductAction -> Database error: : 
Invalid `prisma.productDiscount.create()` invocation:


Unique constraint failed on the fields: (`productId`)
2025-05-19 10:17:43 [ERROR]: discountAddProductAction -> Database error: : 
Invalid `prisma.productDiscount.create()` invocation:


Unique constraint failed on the fields: (`productId`)
2025-05-19 10:24:57 [ERROR]: discountAddProductAction -> Product "01200411861" not found.: undefined
2025-05-19 10:25:05 [ERROR]: discountAddProductAction -> Database error: : 
Invalid `prisma.productDiscount.create()` invocation:


Unique constraint failed on the fields: (`productId`)
2025-05-19 10:30:43 [ERROR]: discountAddProductAction -> Database error: : 
Invalid `prisma.productDiscount.create()` invocation:


Unique constraint failed on the fields: (`productId`)
2025-05-19 10:31:07 [ERROR]: discountAddProductAction -> Database error: : 
Invalid `prisma.productDiscount.create()` invocation:


Unique constraint failed on the fields: (`productId`)
2025-05-19 10:34:23 [ERROR]: discountAddProductAction -> Product 01090398983 already has an active discount.: undefined
2025-05-19 10:34:33 [ERROR]: discountAddProductAction -> Product 01090398983 already has an active discount.: undefined
2025-05-19 10:34:38 [ERROR]: discountAddProductAction -> Database error: : 
Invalid `prisma.productDiscount.create()` invocation:


Unique constraint failed on the fields: (`productId`)
2025-05-19 10:35:13 [ERROR]: discountAddProductAction -> Product 01090398983 already has an active discount.: undefined
2025-05-19 10:36:44 [ERROR]: discountAddProductAction -> Product 11127502650 already has an active discount.: undefined
2025-05-19 10:36:48 [ERROR]: discountAddProductAction -> Product 01200426528 already has an active discount.: undefined
2025-05-19 10:36:53 [ERROR]: discountAddProductAction -> Product 01410012466 already has an active discount.: undefined
2025-05-19 10:36:57 [ERROR]: discountAddProductAction -> Database error: : 
Invalid `prisma.productDiscount.create()` invocation:


Unique constraint failed on the fields: (`productId`)
2025-05-19 10:39:13 [ERROR]: discountAddProductAction -> Database error: : 
Invalid `prisma.productDiscount.create()` invocation:


Unique constraint failed on the fields: (`productId`)
2025-05-19 10:40:54 [ERROR]: discountAddProductAction -> Product "12364564764" not found.: undefined
2025-05-19 10:57:14 [ERROR]: discountAddProductAction -> Product 01200426528 already has an active discount.: undefined
2025-05-19 12:15:03 [ERROR]: DiscountAddProductAction -> Database error: PrismaClientKnownRequestError: 
Invalid `prisma.productDiscount.create()` invocation:


Unique constraint failed on the fields: (`productId`)  by cosmin.oprea: undefined
2025-05-19 12:15:10 [ERROR]: DiscountAddProductAction -> Product 01200426528 already has an active discount by cosmin.oprea: undefined
2025-05-19 12:15:14 [ERROR]: DiscountAddProductAction -> Product 11127502650 already has an active discount by cosmin.oprea: undefined
2025-05-19 12:15:20 [ERROR]: DiscountAddProductAction -> Product "13423424234" not found by cosmin.oprea: undefined
2025-05-19 16:27:54 [ERROR]: ProductSearchAction -> Product "11134241231" not found by cosmin.oprea: undefined
2025-05-19 16:28:00 [ERROR]: ProductSearchAction -> Product "11134241231" not found by cosmin.oprea: undefined
2025-05-19 16:28:07 [ERROR]: ProductSearchAction -> Product "11134241231" not found by cosmin.oprea: undefined
2025-05-21 08:54:57 [ERROR]: ProductSearchAction -> Product "12312312312" not found by cosmin.oprea: undefined
2025-05-21 11:51:08 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "undefined",
    "path": [],
    "message": "Required"
  }
] by cosmin.oprea: undefined
2025-05-21 11:53:07 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "undefined",
    "path": [],
    "message": "Required"
  }
] by cosmin.oprea: undefined
2025-05-21 11:53:55 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "undefined",
    "path": [],
    "message": "Required"
  }
] by cosmin.oprea: undefined
2025-05-21 11:55:18 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "undefined",
    "path": [],
    "message": "Required"
  }
] by cosmin.oprea: undefined
2025-05-21 11:56:26 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "undefined",
    "path": [],
    "message": "Required"
  }
] by cosmin.oprea: undefined
2025-05-21 11:58:04 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "undefined",
    "path": [],
    "message": "Required"
  }
] by cosmin.oprea: undefined
2025-05-21 11:58:19 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "undefined",
    "path": [],
    "message": "Required"
  }
] by cosmin.oprea: undefined
2025-05-21 12:00:25 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "undefined",
    "path": [],
    "message": "Required"
  }
] by cosmin.oprea: undefined
2025-05-21 12:13:15 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "undefined",
    "path": [],
    "message": "Required"
  }
] by cosmin.oprea: undefined
2025-05-21 12:15:06 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "string",
    "path": [],
    "message": "Expected object, received string"
  }
] by cosmin.oprea: undefined
2025-05-21 12:15:12 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "string",
    "path": [],
    "message": "Expected object, received string"
  }
] by cosmin.oprea: undefined
2025-05-21 12:15:25 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "string",
    "path": [],
    "message": "Expected object, received string"
  }
] by cosmin.oprea: undefined
2025-05-21 12:15:34 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "string",
    "path": [],
    "message": "Expected object, received string"
  }
] by cosmin.oprea: undefined
2025-05-21 12:16:37 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "string",
    "path": [],
    "message": "Expected object, received string"
  }
] by cosmin.oprea: undefined
2025-05-21 12:17:10 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "string",
    "path": [],
    "message": "Expected object, received string"
  }
] by cosmin.oprea: undefined
2025-05-21 12:17:22 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "string",
    "path": [],
    "message": "Expected object, received string"
  }
] by cosmin.oprea: undefined
2025-05-21 12:17:36 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "string",
    "path": [],
    "message": "Expected object, received string"
  }
] by cosmin.oprea: undefined
2025-05-21 12:18:02 [ERROR]: getProductAttributes -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "object",
    "received": "string",
    "path": [],
    "message": "Expected object, received string"
  }
] by cosmin.oprea: undefined
2025-05-21 15:49:32 [ERROR]: DeleteProductAttributeAction -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "string",
    "received": "undefined",
    "path": [
      "key"
    ],
    "message": "Required"
  }
] by cosmin.oprea: undefined
2025-05-21 15:49:55 [ERROR]: DeleteProductAttributeAction -> Error at parsing: [
  {
    "code": "invalid_type",
    "expected": "string",
    "received": "undefined",
    "path": [
      "key"
    ],
    "message": "Required"
  }
] by cosmin.oprea: undefined
2025-05-21 16:48:22 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:23 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:23 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:23 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:24 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:24 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:37 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:37 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:38 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:38 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:38 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:42 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:42 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:42 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:43 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:43 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:48 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:48 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:48 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:48 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:49 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:52 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:53 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:53 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:53 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:53 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:56 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:57 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:57 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:57 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:48:58 [ERROR]: getProductAttributes -> Product "11127502650" not found by cosmin.oprea: undefined
2025-05-21 16:59:23 [ERROR]: ProductSearchAction -> Error for 11127502650 with success by cosmin.oprea: undefined
2025-05-22 07:53:36 [ERROR]: ProductSearchAction -> Error for 11127502650 with success by cosmin.oprea: undefined
