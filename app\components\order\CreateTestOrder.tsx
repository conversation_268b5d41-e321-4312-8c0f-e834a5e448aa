"use client";

import { useState, useTransition } from "react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { createTestOrder } from "@/app/actions/testActions";
import { useRouter } from "next/navigation";

export default function CreateTestOrder() {
  const [isPending, startTransition] = useTransition();
  const router = useRouter();

  const handleCreateOrder = () => {
    startTransition(async () => {
      try {
        const response = await createTestOrder();
        
        if (response.status === "SUCCESS") {
          toast.success(response.message);
          
          // If we have the order ID, navigate to it
          if (response.data?.orderId) {
            router.push(`/orders/${response.data.orderId}`);
          } else {
            // Otherwise just refresh the orders page
            router.refresh();
          }
        } else {
          toast.error(response.message);
        }
      } catch (error) {
        toast.error("An unexpected error occurred");
        console.error(error);
      }
    });
  };

  return (
    <Button 
      onClick={handleCreateOrder} 
      disabled={isPending}
      variant="outline"
    >
      {isPending ? "Creating..." : "Create Test Order"}
    </Button>
  );
}