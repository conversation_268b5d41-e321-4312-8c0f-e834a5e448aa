
import { notFound } from "next/navigation";
import DiscountUpdateForm from "@/app/components/discount/DiscountUpdateForm";
import prisma from "@/app/utils/db";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { requireAdminOrModerator } from "@/lib/auth-utils";

interface UpdatePageProps {
  params: Promise<{ id: string }> 
}

export default async function UpdateDiscountPage({ params }: UpdatePageProps) {
  await requireAdminOrModerator();
  const paramsObject = await params;
  const idAsString = paramsObject.id;    //All params are strings and need to be parsed
  const id = idAsString
 
  const discount = await prisma.discount.findUnique({
    where: { id },
  });

  if (!discount) {
    return notFound();
  }

  return (

      <div className="mx-auto w-1/4 border-2 border-gray-100 p-4 rounded-lg">
        <h2 className="text-2xl font-bold">Update Discount</h2>
        <Link href="/discounts"><Button variant="outline" className="my-4">Go back</Button></Link>
        <DiscountUpdateForm
          id={discount.id} 
          defaultValues={{
            name: discount.name,
            description: discount.description ?? "",
            type: discount.type,
            value: discount.value.toNumber(),
            startDate: discount.startDate,
            endDate: discount.endDate,
            active: discount.active,
          }} 
        />
      </div>

  );
}
