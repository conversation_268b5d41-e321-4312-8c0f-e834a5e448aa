import prisma from "@/app/utils/db";


export async function getUserStats() {
  try {
    // Get current date
    const now = new Date();
    
    // Calculate date 30 days ago
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    // Calculate date 60 days ago (for growth comparison)
    const sixtyDaysAgo = new Date();
    sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);
    
    // Calculate date 24 hours ago
    const twentyFourHoursAgo = new Date();
    twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);
    
    // Run all queries in parallel for better performance
    const [
      totalUsers,
      activeUsers,
      newUsers30Days,
      newUsersPrevious30Days,
      loggedInLast24h
    ] = await Promise.all([
      // Total users (excluding deleted)
      prisma.user.count({
        where: { deletedAt: null }
      }),
      
      // Active users (not suspended, not deleted)
      prisma.user.count({
        where: {
          isActive: true,
          isSuspended: false,
          deletedAt: null
        }
      }),
      
      // New users in last 30 days
      prisma.user.count({
        where: {
          createdAt: { gte: thirtyDaysAgo },
          deletedAt: null
        }
      }),
      
      // New users in previous 30 days (for growth calculation)
      prisma.user.count({
        where: {
          createdAt: { 
            gte: sixtyDaysAgo,
            lt: thirtyDaysAgo
          },
          deletedAt: null
        }
      }),
      
      // Users logged in within last 24 hours
      prisma.user.count({
        where: {
          lastLoginAt: { gte: twentyFourHoursAgo },
          deletedAt: null
        }
      })
    ]);
    
    // Calculate percentages and growth
    const activePercentage = totalUsers > 0 
      ? Math.round((activeUsers / totalUsers) * 100) 
      : 0;
    
    const newUsersGrowth = newUsersPrevious30Days > 0 
      ? Math.round(((newUsers30Days - newUsersPrevious30Days) / newUsersPrevious30Days) * 100) 
      : newUsers30Days > 0 ? 100 : 0;
    
    return {
      totalUsers,
      activeUsers,
      activePercentage,
      newUsers30Days,
      newUsersGrowth,
      loggedInLast24h
    };
  } catch (error) {
    console.error("Error fetching user stats:", error);
    // Return default values in case of error
    return {
      totalUsers: 0,
      activeUsers: 0,
      activePercentage: 0,
      newUsers30Days: 0,
      newUsersGrowth: 0,
      loggedInLast24h: 0
    };
  }
}